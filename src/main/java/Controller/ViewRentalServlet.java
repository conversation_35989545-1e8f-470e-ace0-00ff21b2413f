package Controller;

import java.io.IOException;

import DAO.RentalDetailsDAO;
import Model.RentalDetails;
import Model.User;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

@WebServlet("/view-rental/*")
public class ViewRentalServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        System.out.println("\n=== ViewRentalServlet: doGet method called ===");
        
        // Check if user is logged in
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            System.out.println("User not logged in, redirecting to login page");
            response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
            return;
        }
        
        User user = (User) session.getAttribute("user");
        System.out.println("User logged in: " + user.getUserName());
        
        // Get rental ID from path
        String pathInfo = request.getPathInfo();
        if (pathInfo == null || pathInfo.equals("/")) {
            System.out.println("No rental ID provided");
            response.sendRedirect(request.getContextPath() + "/user-rentals");
            return;
        }
        
        String rentalIdStr = pathInfo.substring(1);
        System.out.println("Rental ID from path: " + rentalIdStr);
        
        try {
            int rentalDetailId = Integer.parseInt(rentalIdStr);
            
            // Get rental details
            RentalDetailsDAO rentalDAO = new RentalDetailsDAO();
            RentalDetails rental = rentalDAO.getRentalById(rentalDetailId);
            
            if (rental == null) {
                System.out.println("Rental not found");
                request.setAttribute("errorMessage", "Rental not found");
                request.getRequestDispatcher("/View/viewRentals.jsp").forward(request, response);
                return;
            }
            
            // Check if rental belongs to the logged-in user or user is admin
            if (rental.getUserId() != user.getUserId() && !"admin".equalsIgnoreCase(user.getUserType())) {
                System.out.println("Rental does not belong to the logged-in user");
                request.setAttribute("errorMessage", "You do not have permission to view this rental");
                request.getRequestDispatcher("/View/viewRentals.jsp").forward(request, response);
                return;
            }
            
            // Set rental in request and forward to view page
            request.setAttribute("rental", rental);
            
            // Forward to appropriate view based on user type
            if ("admin".equalsIgnoreCase(user.getUserType())) {
                request.getRequestDispatcher("/View/adminViewRental.jsp").forward(request, response);
            } else {
                request.getRequestDispatcher("/View/viewRental.jsp").forward(request, response);
            }
            
        } catch (NumberFormatException e) {
            System.err.println("Invalid rental ID format: " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/user-rentals");
        }
    }
}
