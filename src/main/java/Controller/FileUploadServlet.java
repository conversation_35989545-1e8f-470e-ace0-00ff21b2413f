package Controller;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.file.Paths;
import java.util.UUID;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.MultipartConfig;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.Part;

import org.json.JSONObject;

// Servlet mapping defined in web.xml
public class FileUploadServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;

    // Directory to store uploaded files relative to the web application
    private static final String UPLOAD_DIRECTORY = "assets/images/books";

    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        PrintWriter out = response.getWriter();
        JSONObject jsonResponse = new JSONObject();

        try {
            System.out.println("FileUploadServlet: Processing file upload request");
            System.out.println("Content type: " + request.getContentType());

            // Get the file part from the request
            Part filePart = request.getPart("image");
            if (filePart == null) {
                throw new ServletException("No file uploaded");
            }

            System.out.println("File part found: " + filePart.getSubmittedFileName());
            System.out.println("File size: " + filePart.getSize());

            // Get the file name from the part
            String fileName = Paths.get(filePart.getSubmittedFileName()).getFileName().toString();
            if (fileName == null || fileName.isEmpty()) {
                throw new ServletException("Invalid file name");
            }

            // Generate a unique file name to prevent overwriting
            String fileExtension = fileName.substring(fileName.lastIndexOf("."));
            String uniqueFileName = UUID.randomUUID().toString() + fileExtension;

            // Get the absolute path to the upload directory
            String applicationPath = request.getServletContext().getRealPath("");
            String uploadPath = applicationPath + File.separator + UPLOAD_DIRECTORY;

            System.out.println("Upload path: " + uploadPath);

            // Create the upload directory if it doesn't exist
            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                boolean created = uploadDir.mkdirs();
                if (!created) {
                    throw new IOException("Failed to create upload directory: " + uploadPath);
                }
                System.out.println("Created upload directory: " + uploadPath);
            }

            // Save the file to the upload directory
            String filePath = uploadPath + File.separator + uniqueFileName;
            System.out.println("Saving file to: " + filePath);

            // Write the file
            filePart.write(filePath);

            // Verify the file was created
            File uploadedFile = new File(filePath);
            if (!uploadedFile.exists()) {
                throw new IOException("File was not created: " + filePath);
            }

            System.out.println("File saved successfully: " + filePath);
            System.out.println("File size: " + uploadedFile.length() + " bytes");

            // Return the relative path to the file
            String relativePath = UPLOAD_DIRECTORY + "/" + uniqueFileName;

            jsonResponse.put("success", true);
            jsonResponse.put("message", "File uploaded successfully");
            jsonResponse.put("filePath", relativePath);

        } catch (Exception e) {
            jsonResponse.put("success", false);
            jsonResponse.put("message", "Error uploading file: " + e.getMessage());
            System.err.println("Error in FileUploadServlet: " + e.getMessage());
            e.printStackTrace();

            // Log more details about the error
            System.err.println("Upload directory: " + request.getServletContext().getRealPath("") + File.separator + UPLOAD_DIRECTORY);
            System.err.println("Content type: " + request.getContentType());
            try {
                Part filePart = request.getPart("image");
                System.err.println("File part found: " + (filePart != null));
                if (filePart != null) {
                    System.err.println("File name: " + filePart.getSubmittedFileName());
                    System.err.println("File size: " + filePart.getSize());
                }
            } catch (Exception ex) {
                System.err.println("Error getting file part: " + ex.getMessage());
            }
        }

        out.print(jsonResponse.toString());
        out.flush();
    }
}
