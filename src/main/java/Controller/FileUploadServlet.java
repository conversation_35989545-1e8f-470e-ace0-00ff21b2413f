package Controller;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.file.Paths;
import java.util.UUID;

import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.MultipartConfig;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.Part;

import org.json.JSONObject;

@WebServlet("/upload-image")
@MultipartConfig(
    fileSizeThreshold = 1024 * 1024 * 1, // 1 MB
    maxFileSize = 1024 * 1024 * 10,      // 10 MB
    maxRequestSize = 1024 * 1024 * 15,   // 15 MB
    location = "/tmp"
)
public class FileUploadServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;
    
    // Directory to store uploaded files relative to the web application
    private static final String UPLOAD_DIRECTORY = "assets/images/books";
    
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        response.setContentType("application/json");
        PrintWriter out = response.getWriter();
        JSONObject jsonResponse = new JSONObject();
        
        try {
            // Get the file part from the request
            Part filePart = request.getPart("image");
            
            // Get the file name from the part
            String fileName = Paths.get(filePart.getSubmittedFileName()).getFileName().toString();
            
            // Generate a unique file name to prevent overwriting
            String fileExtension = fileName.substring(fileName.lastIndexOf("."));
            String uniqueFileName = UUID.randomUUID().toString() + fileExtension;
            
            // Get the absolute path to the upload directory
            String applicationPath = request.getServletContext().getRealPath("");
            String uploadPath = applicationPath + File.separator + UPLOAD_DIRECTORY;
            
            // Create the upload directory if it doesn't exist
            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }
            
            // Save the file to the upload directory
            String filePath = uploadPath + File.separator + uniqueFileName;
            filePart.write(filePath);
            
            // Return the relative path to the file
            String relativePath = UPLOAD_DIRECTORY + "/" + uniqueFileName;
            
            jsonResponse.put("success", true);
            jsonResponse.put("message", "File uploaded successfully");
            jsonResponse.put("filePath", relativePath);
            
        } catch (Exception e) {
            jsonResponse.put("success", false);
            jsonResponse.put("message", "Error uploading file: " + e.getMessage());
            e.printStackTrace();
        }
        
        out.print(jsonResponse.toString());
        out.flush();
    }
}
