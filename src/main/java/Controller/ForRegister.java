package Controller;
import java.io.IOException;
import java.sql.SQLException;

import DAO.UserDAO;
import Model.User;
import Utils.PasswordHash;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;


@WebServlet(name = "ForRegister", value = "/ForRegister")
public class ForRegister extends HttpServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // Forward GET requests to the registration page
        request.getRequestDispatcher("/View/Register.jsp").forward(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException
    {
        System.out.println("\n=== ForRegister: doPost method called ===");
        System.out.println("Request URL: " + request.getRequestURL());

        // Getting the values from the Form
        String username = request.getParameter("username");
        String email = request.getParameter("email");
        String password = request.getParameter("password");
        String confirmPassword = request.getParameter("confirmPassword");

        // Validate input parameters
        if (username == null || email == null || password == null || confirmPassword == null) {
            System.out.println("Missing required parameters");
            request.setAttribute("errorMessage", "All fields are required");
            request.getRequestDispatcher("/View/Register.jsp").forward(request, response);
            return;
        }

        username = username.trim();
        email = email.trim();
        password = password.trim();
        confirmPassword = confirmPassword.trim();

        if (username.isEmpty() || email.isEmpty() || password.isEmpty() || confirmPassword.isEmpty()) {
            System.out.println("Empty fields detected");
            request.setAttribute("errorMessage", "All fields are required");
            request.getRequestDispatcher("/View/Register.jsp").forward(request, response);
            return;
        }

        // Checking email duplication
        try
        {
            if(UserDAO.checkEmail(email))
            {
                // If the email already exists, redirect to the register page with an error message
                System.out.println("Email already exists: " + email);
                request.setAttribute("errorMessage", "Email already exists");
                request.getRequestDispatcher("/View/Register.jsp").forward(request, response);
                return;
            }
            else {
                System.out.println("Email does not exist: " + email);

                // Checking Password Duplication
                if (!password.equals(confirmPassword))
                {
                    // If the passwords don't match, redirect to the register page with an error message
                    System.out.println("Passwords don't match");
                    request.setAttribute("errorMessage", "Passwords do not match");
                    request.getRequestDispatcher("/View/Register.jsp").forward(request, response);
                    return;
                }
                else
                {
                    System.out.println("Passwords match");
                    // Creating a new User object
                    String hashedPassword = password; // Store plain password for now for easier testing
                    try {
                        hashedPassword = PasswordHash.hash(password);
                    } catch (Exception e) {
                        System.out.println("Error hashing password: " + e.getMessage());
                        // Continue with plain password if hashing fails
                    }

                    System.out.println("Original password: " + password);
                    System.out.println("Hashed/stored password: " + hashedPassword);

                    User userObject = new User(username, email, hashedPassword);

                    try
                    {
                        // Register the user
                        UserDAO.register(userObject);

                        // Set success message and redirect to login page
                        request.setAttribute("successMessage", "Registration successful! Please log in.");
                        request.getRequestDispatcher("/View/LogIn.jsp").forward(request, response);
                        return;
                    }
                    catch (SQLException e)
                    {
                        System.err.println("Database Error: " + e.getMessage());
                        e.printStackTrace();
                        request.setAttribute("errorMessage", "Database error occurred. Please try again later.");
                        request.getRequestDispatcher("/View/Register.jsp").forward(request, response);
                        return;
                    }
                }
            }
        }
        catch (SQLException e)
        {
            System.err.println("SQL Error: " + e.getMessage());
            e.printStackTrace();
            request.setAttribute("errorMessage", "Database error occurred. Please try again later.");
            request.getRequestDispatcher("/View/Register.jsp").forward(request, response);
            return;
        }
        catch (Exception e)
        {
            System.err.println("Unexpected error: " + e.getMessage());
            e.printStackTrace();
            request.setAttribute("errorMessage", "An unexpected error occurred. Please try again later.");
            request.getRequestDispatcher("/View/Register.jsp").forward(request, response);
            return;
        }
    }
}