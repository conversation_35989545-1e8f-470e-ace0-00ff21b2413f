package Controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import DAO.BookDAO;
import Model.Book;
import java.sql.SQLException;

/**
 * Servlet implementation class SearchServlet
 * Handles search requests for books
 * Servlet mapping defined in web.xml
 */
public class SearchServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;

    /**
     * @see HttpServlet#HttpServlet()
     */
    public SearchServlet() {
        super();
    }

    /**
     * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
     */
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        System.out.println("\n=== SearchServlet: doGet method called ===");

        // Get the search query parameter
        String query = request.getParameter("query");
        System.out.println("Search query: " + query);

        // If query is empty or null, redirect to home page
        if (query == null || query.trim().isEmpty()) {
            System.out.println("Empty query, redirecting to home page");
            response.sendRedirect(request.getContextPath() + "/index.jsp");
            return;
        }

        try {
            List<Book> searchResults = null;

            // First try using BookDAO from DAO package
            try {
                System.out.println("Trying to search using DAO.BookDAO");
                DAO.BookDAO bookDAO = new DAO.BookDAO();
                searchResults = bookDAO.searchBooks(query);
                System.out.println("Found " + searchResults.size() + " results for query: " + query + " using DAO.BookDAO");
            } catch (Exception e) {
                System.err.println("Error using DAO.BookDAO: " + e.getMessage());
                e.printStackTrace();

                // If that fails, try using BookDAO from Model package
                try {
                    System.out.println("Trying to search using Model.BookDAO");
                    Model.BookDAO modelBookDAO = new Model.BookDAO();
                    searchResults = modelBookDAO.searchBooks(query);
                    System.out.println("Found " + searchResults.size() + " results for query: " + query + " using Model.BookDAO");
                } catch (Exception ex) {
                    System.err.println("Error using Model.BookDAO: " + ex.getMessage());
                    ex.printStackTrace();
                    throw ex; // Re-throw the exception if both methods fail
                }
            }

            if (searchResults == null) {
                searchResults = new ArrayList<>();
                System.out.println("No search results found or error occurred");
            }

            // Set attributes for the search results page
            request.setAttribute("query", query);
            request.setAttribute("searchResults", searchResults);
            request.setAttribute("resultCount", searchResults.size());

            // Forward to the search results page
            System.out.println("Forwarding to searchResults.jsp");
            request.getRequestDispatcher("/View/searchResults.jsp").forward(request, response);
        } catch (Exception e) {
            System.err.println("Error in SearchServlet: " + e.getMessage());
            e.printStackTrace();

            // Set error attributes
            request.setAttribute("query", query);
            request.setAttribute("searchResults", null);
            request.setAttribute("resultCount", 0);
            request.setAttribute("errorMessage", "An error occurred while searching. Please try again later.");

            // Forward to the search results page with error
            request.getRequestDispatcher("/View/searchResults.jsp").forward(request, response);
        }
    }

    /**
     * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
     */
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // Forward POST requests to doGet
        doGet(request, response);
    }
}
