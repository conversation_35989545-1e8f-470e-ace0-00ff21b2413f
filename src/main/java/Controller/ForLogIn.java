package Controller;

import java.io.IOException;
import java.sql.SQLException;

import Model.User;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;


@WebServlet(name = "ForLogIn", value = "/ForLogIn")
public class ForLogIn extends HttpServlet {
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // Handle GET requests by forwarding to doPost
        doPost(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        System.out.println("\n=== ForLogIn: doPost method called ===");
        System.out.println("Request URL: " + request.getRequestURL());

        String email;
        String password;

        // Get parameters from request
        email = request.getParameter("email");
        password = request.getParameter("password");

        if (email == null || password == null) {
            System.out.println("Email or password is null");
            request.setAttribute("errorMessage", "Email and password are required");
            request.getRequestDispatcher("/View/LogIn.jsp").forward(request, response);
            return;
        }

        email = email.trim();
        password = password.trim();

        System.out.println("Login attempt with email: " + email);

        //Getting the session
        HttpSession session = request.getSession();

        try {
            // Use the new validateUser method
            User user = UserDAO.validateUser(email, password);

            if (user != null) {
                System.out.println("Login successful for email: " + email);

                // Store user in session
                session.setAttribute("user", user);

                System.out.println("User type: " + user.getUserType());

                // Redirect based on user type
                if("admin".equalsIgnoreCase(user.getUserType())) {
                    System.out.println("Redirecting to admin dashboard");
                    response.sendRedirect(request.getContextPath() + "/View/AdminDashboard.jsp");
                    return;
                } else {
                    System.out.println("Redirecting to user dashboard");
                    response.sendRedirect(request.getContextPath() + "/View/UserDashboard.jsp");
                    return;
                }
            } else {
                System.out.println("Login Failed - Wrong Email or Password");
                request.setAttribute("errorMessage", "Invalid Email or Password");
                request.getRequestDispatcher("/View/LogIn.jsp").forward(request, response);
                return;
            }
        } catch (SQLException e) {
            System.err.println("Database error during login: " + e.getMessage());
            e.printStackTrace();
            request.setAttribute("errorMessage", "Database error occurred. Please try again later.");
            request.getRequestDispatcher("/View/LogIn.jsp").forward(request, response);
            return;
        } catch (Exception e) {
            System.err.println("Unexpected error during login: " + e.getMessage());
            e.printStackTrace();
            request.setAttribute("errorMessage", "An unexpected error occurred. Please try again later.");
            request.getRequestDispatcher("/View/LogIn.jsp").forward(request, response);
            return;
        }
    }
}