package Controller;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

import DAO.UserDAO;
import Model.User;
import Model.DirectSQLExecutor;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

@WebServlet("/admin/users/*")
public class AdminUserManagementServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        HttpSession session = request.getSession();
        User user = (User) session.getAttribute("user");

        // Check if user is logged in and is an admin
        if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
            response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
            return;
        }

        String pathInfo = request.getPathInfo();

        try {
            if (pathInfo == null || pathInfo.equals("/")) {
                // List all users
                UserDAO userDAO = new UserDAO();
                List<User> users = userDAO.getAllUsers();
                request.setAttribute("users", users);
                request.getRequestDispatcher("/View/adminUsers.jsp").forward(request, response);
            } else if (pathInfo.startsWith("/view/")) {
                // View user details
                int userId = Integer.parseInt(pathInfo.substring(6));
                UserDAO userDAO = new UserDAO();
                User viewUser = userDAO.getUserById(userId);

                if (viewUser != null) {
                    request.setAttribute("viewUser", viewUser);
                    request.getRequestDispatcher("/View/adminViewUser.jsp").forward(request, response);
                } else {
                    request.setAttribute("errorMessage", "User not found");
                    response.sendRedirect(request.getContextPath() + "/admin/users");
                }
            } else if (pathInfo.startsWith("/edit/")) {
                // Show edit form
                int userId = Integer.parseInt(pathInfo.substring(6));
                UserDAO userDAO = new UserDAO();
                User editUser = userDAO.getUserById(userId);

                if (editUser != null) {
                    request.setAttribute("editUser", editUser);
                    request.getRequestDispatcher("/View/adminEditUser.jsp").forward(request, response);
                } else {
                    request.setAttribute("errorMessage", "User not found");
                    response.sendRedirect(request.getContextPath() + "/admin/users");
                }
            } else {
                response.sendRedirect(request.getContextPath() + "/admin/users");
            }
        } catch (SQLException e) {
            e.printStackTrace();
            request.setAttribute("errorMessage", "Database error: " + e.getMessage());
            request.getRequestDispatcher("/View/AdminDashboard.jsp").forward(request, response);
        } catch (NumberFormatException e) {
            response.sendRedirect(request.getContextPath() + "/admin/users");
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        HttpSession session = request.getSession();
        User user = (User) session.getAttribute("user");

        // Check if user is logged in and is an admin
        if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
            response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
            return;
        }

        String action = request.getParameter("action");

        try {
            if ("update".equals(action)) {
                // Update user
                int userId = Integer.parseInt(request.getParameter("userId"));
                String username = request.getParameter("username");
                String email = request.getParameter("email");
                String fullName = request.getParameter("fullName");
                String userType = request.getParameter("userType");

                User updateUser = new User();
                updateUser.setUserId(userId);
                updateUser.setUserName(username);
                updateUser.setEmail(email);
                updateUser.setFullName(fullName);
                updateUser.setUserType(userType);

                boolean success = UserDAO.updateUser(updateUser);

                if (success) {
                    session.setAttribute("successMessage", "User updated successfully");
                } else {
                    session.setAttribute("errorMessage", "Failed to update user");
                }

                response.sendRedirect(request.getContextPath() + "/admin/users");
            } else if ("delete".equals(action)) {
                // Delete user
                String userIdParam = request.getParameter("userId");
                System.out.println("Delete action received for user ID: " + userIdParam);

                if (userIdParam == null || userIdParam.isEmpty()) {
                    System.out.println("User ID parameter is null or empty");
                    session.setAttribute("errorMessage", "User ID is required");
                    response.sendRedirect(request.getContextPath() + "/admin/users");
                    return;
                }

                try {
                    int userId = Integer.parseInt(userIdParam);
                    System.out.println("Parsed user ID: " + userId);

                    // Don't allow admin to delete themselves
                    if (userId == user.getUserId()) {
                        System.out.println("Attempted to delete own admin account");
                        session.setAttribute("errorMessage", "You cannot delete your own admin account");
                        response.sendRedirect(request.getContextPath() + "/admin/users");
                        return;
                    }

                    System.out.println("Using DirectSQLExecutor to force delete user ID: " + userId);
                    boolean success = DirectSQLExecutor.forceDeleteUser(userId);
                    System.out.println("DirectSQLExecutor.forceDeleteUser result: " + success);

                    if (success) {
                        session.setAttribute("successMessage", "User deleted successfully");
                    } else {
                        session.setAttribute("errorMessage", "Failed to delete user. Please check server logs for details.");
                    }
                } catch (NumberFormatException e) {
                    System.out.println("Error parsing user ID: " + e.getMessage());
                    session.setAttribute("errorMessage", "Invalid user ID format");
                }

                response.sendRedirect(request.getContextPath() + "/admin/users");
            } else {
                response.sendRedirect(request.getContextPath() + "/admin/users");
            }
        } catch (SQLException e) {
            e.printStackTrace();
            session.setAttribute("errorMessage", "Database error: " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/admin/users");
        } catch (NumberFormatException e) {
            e.printStackTrace();
            session.setAttribute("errorMessage", "Invalid user ID format");
            response.sendRedirect(request.getContextPath() + "/admin/users");
        } catch (Exception e) {
            e.printStackTrace();
            session.setAttribute("errorMessage", "Unexpected error: " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/admin/users");
        }
    }
}
