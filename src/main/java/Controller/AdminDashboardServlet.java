package Controller;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

import DAO.BookDAO;
import DAO.RentalDetailsDAO;
import Model.Book;
import Model.RentalDetails;
import Model.User;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

@WebServlet("/admin/dashboard")
public class AdminDashboardServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        HttpSession session = request.getSession();
        User user = (User) session.getAttribute("user");
        
        // Check if user is logged in and is an admin
        if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
            response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
            return;
        }
        
        try {
            // Get dashboard statistics
            int totalUsers = UserDAO.getTotalUsers();
            int activeRentals = RentalDetailsDAO.getActiveRentalsCount();
            int pendingRentals = RentalDetailsDAO.getPendingRentalsCount();
            double totalRevenue = RentalDetailsDAO.getTotalRevenue();
            
            // Get recent users
            List<User> recentUsers = UserDAO.getRecentUsers(5);
            
            // Get recent rentals
            RentalDetailsDAO rentalDAO = new RentalDetailsDAO();
            List<RentalDetails> recentRentals = rentalDAO.getRecentRentals(5);
            
            // Get popular books
            BookDAO bookDAO = new BookDAO();
            List<Book> popularBooks = bookDAO.getPopularBooks(5);
            
            // Set attributes for the JSP
            request.setAttribute("totalUsers", totalUsers);
            request.setAttribute("activeRentals", activeRentals);
            request.setAttribute("pendingRentals", pendingRentals);
            request.setAttribute("totalRevenue", totalRevenue);
            request.setAttribute("recentUsers", recentUsers);
            request.setAttribute("recentRentals", recentRentals);
            request.setAttribute("popularBooks", popularBooks);
            
            // Forward to the admin dashboard JSP
            request.getRequestDispatcher("/View/AdminDashboard.jsp").forward(request, response);
        } catch (SQLException e) {
            e.printStackTrace();
            request.setAttribute("errorMessage", "Database error: " + e.getMessage());
            request.getRequestDispatcher("/View/AdminDashboard.jsp").forward(request, response);
        }
    }
}
