package Controller;

import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;

import Model.User;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.MultipartConfig;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import jakarta.servlet.http.Part;

@WebServlet("/UpdateProfilePicture")
@MultipartConfig(
    maxFileSize = 5 * 1024 * 1024, // 5MB
    maxRequestSize = 6 * 1024 * 1024, // 6MB
    fileSizeThreshold = 1024 * 1024 // 1MB
)
public class UpdateProfilePicture extends HttpServlet {
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        HttpSession session = request.getSession();
        User user = (User) session.getAttribute("user");
        
        if (user == null) {
            response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
            return;
        }

        try {
            Part filePart = request.getPart("profilePicture");
            if (filePart != null && filePart.getSize() > 0) {
                // Check file size again (in case client-side validation was bypassed)
                if (filePart.getSize() > 5 * 1024 * 1024) {
                    request.setAttribute("errorMessage", "File size exceeds 5MB limit. Please choose a smaller image.");
                    request.getRequestDispatcher("/View/UserDashboard.jsp").forward(request, response);
                    return;
                }

                System.out.println("File size: " + filePart.getSize() + " bytes");
                System.out.println("File type: " + filePart.getContentType());

                InputStream fileContent = filePart.getInputStream();
                byte[] imageBytes = fileContent.readAllBytes();
                String base64Image = Base64.getEncoder().encodeToString(imageBytes);
                
                System.out.println("Base64 image length: " + base64Image.length());
                
                // Update user's profile picture in the database
                boolean success = UserDAO.updateProfilePicture(user.getUserId(), base64Image);
                System.out.println("Database update success: " + success);
                
                if (success) {
                    // Update user object in session
                    user.setProfilePicture(base64Image);
                    session.setAttribute("user", user);
                    
                    // Redirect back to dashboard
                    response.sendRedirect(request.getContextPath() + "/View/UserDashboard.jsp");
                } else {
                    request.setAttribute("errorMessage", "Failed to update profile picture. Please try again.");
                    request.getRequestDispatcher("/View/UserDashboard.jsp").forward(request, response);
                }
            } else {
                System.out.println("No file was uploaded");
                response.sendRedirect(request.getContextPath() + "/View/UserDashboard.jsp");
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e.getMessage() != null && e.getMessage().contains("exceeds its maximum permitted size")) {
                request.setAttribute("errorMessage", "File size exceeds 5MB limit. Please choose a smaller image.");
            } else {
                request.setAttribute("errorMessage", "Error updating profile picture. Please try again.");
            }
            request.getRequestDispatcher("/View/UserDashboard.jsp").forward(request, response);
        }
    }
} 