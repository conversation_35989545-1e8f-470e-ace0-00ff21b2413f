package Controller;

import DAO.ReportDAO;
import Model.User;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@WebServlet("/admin/reports/*")
public class AdminReportServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        HttpSession session = request.getSession();
        User user = (User) session.getAttribute("user");

        // Check if user is logged in and is an admin
        if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
            response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
            return;
        }

        String pathInfo = request.getPathInfo();

        // Default date range (last 30 days)
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(30);

        ReportDAO reportDAO = new ReportDAO();

        System.out.println("AdminReportServlet: pathInfo = " + pathInfo);

        try {
            if (pathInfo == null || pathInfo.equals("/")) {
                // Show reports dashboard
                System.out.println("Forwarding to adminReports.jsp");
                request.getRequestDispatcher("/View/adminReports.jsp").forward(request, response);
            } else if (pathInfo.equals("/rental-activity")) {
                System.out.println("Generating rental activity report");
                // Generate rental activity report
                Map<String, Object> report = reportDAO.getRentalActivityReport(startDate, endDate);
                request.setAttribute("report", report);
                request.setAttribute("startDate", startDate);
                request.setAttribute("endDate", endDate);
                request.getRequestDispatcher("/View/rentalActivityReport.jsp").forward(request, response);
            } else if (pathInfo.equals("/popular-books")) {
                System.out.println("Generating popular books report");
                // Generate popular books report
                List<Map<String, Object>> report = reportDAO.getPopularBooksReport(startDate, endDate, 10);
                request.setAttribute("report", report);
                request.setAttribute("startDate", startDate);
                request.setAttribute("endDate", endDate);
                request.getRequestDispatcher("/View/popularBooksReport.jsp").forward(request, response);
            } else if (pathInfo.equals("/user-activity")) {
                System.out.println("Generating user activity report");
                // Generate user activity report
                List<Map<String, Object>> report = reportDAO.getUserActivityReport(startDate, endDate, 10);
                request.setAttribute("report", report);
                request.setAttribute("startDate", startDate);
                request.setAttribute("endDate", endDate);
                request.getRequestDispatcher("/View/userActivityReport.jsp").forward(request, response);
            } else if (pathInfo.equals("/test")) {
                // Simple test to verify the servlet is working
                response.setContentType("text/html");
                response.getWriter().println("<html><body>");
                response.getWriter().println("<h1>AdminReportServlet Test</h1>");
                response.getWriter().println("<p>The servlet is working correctly!</p>");
                response.getWriter().println("<p>Path Info: " + pathInfo + "</p>");
                response.getWriter().println("<p><a href='" + request.getContextPath() + "/admin/reports'>Back to Reports</a></p>");
                response.getWriter().println("</body></html>");
            } else {
                response.sendRedirect(request.getContextPath() + "/admin/reports");
            }
        } catch (Exception e) {
            System.out.println("Error in AdminReportServlet: " + e.getMessage());
            e.printStackTrace();
            // If there's an error, redirect to the reports dashboard
            response.sendRedirect(request.getContextPath() + "/admin/reports");
        }

        System.out.println("AdminReportServlet processing complete");
    }
}