package Controller;

import DAO.ReportDAO;
import Model.User;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@WebServlet("/admin/reports/*")
public class AdminReportServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        HttpSession session = request.getSession();
        User user = (User) session.getAttribute("user");

        // Check if user is logged in and is an admin
        if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
            response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
            return;
        }

        String pathInfo = request.getPathInfo();

        // Default date range (last 30 days)
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(30);

        // Get date range from request if provided
        String startDateStr = request.getParameter("startDate");
        String endDateStr = request.getParameter("endDate");

        if (startDateStr != null && !startDateStr.isEmpty()) {
            startDate = LocalDate.parse(startDateStr);
        }

        if (endDateStr != null && !endDateStr.isEmpty()) {
            endDate = LocalDate.parse(endDateStr);
        }

        ReportDAO reportDAO = new ReportDAO();

        if (pathInfo == null || pathInfo.equals("/")) {
            // Show reports dashboard with summary data
            try {
                // Get rental activity summary
                Map<String, Object> rentalActivity = reportDAO.getRentalActivityReport(startDate, endDate);
                request.setAttribute("totalRentals", rentalActivity.get("totalRentals"));
                request.setAttribute("activeRentals", rentalActivity.get("activeRentals"));
                request.setAttribute("completedRentals", rentalActivity.get("completedRentals"));
                request.setAttribute("totalRevenue", rentalActivity.get("totalRevenue"));

                // Get top users (limit to 5)
                List<Map<String, Object>> topUsers = reportDAO.getUserActivityReport(startDate, endDate, 5);
                request.setAttribute("topUsers", topUsers);

                // Get top books (limit to 5)
                List<Map<String, Object>> topBooks = reportDAO.getPopularBooksReport(startDate, endDate, 5);
                request.setAttribute("topBooks", topBooks);

                request.getRequestDispatcher("/View/adminReports.jsp").forward(request, response);
            } catch (Exception e) {
                e.printStackTrace();
                // Even if there's an error, still show the dashboard with empty data
                request.getRequestDispatcher("/View/adminReports.jsp").forward(request, response);
            }
        } else if (pathInfo.equals("/rental-activity")) {
            // Generate rental activity report
            Map<String, Object> report = reportDAO.getRentalActivityReport(startDate, endDate);
            request.setAttribute("report", report);
            request.setAttribute("reportType", "Rental Activity");
            request.setAttribute("startDate", startDate);
            request.setAttribute("endDate", endDate);
            request.getRequestDispatcher("/View/adminReportView.jsp").forward(request, response);
        } else if (pathInfo.equals("/popular-books")) {
            // Generate popular books report
            int limit = 10;
            String limitStr = request.getParameter("limit");
            if (limitStr != null && !limitStr.isEmpty()) {
                limit = Integer.parseInt(limitStr);
            }

            List<Map<String, Object>> report = reportDAO.getPopularBooksReport(startDate, endDate, limit);
            request.setAttribute("report", report);
            request.setAttribute("reportType", "Popular Books");
            request.setAttribute("startDate", startDate);
            request.setAttribute("endDate", endDate);
            request.getRequestDispatcher("/View/adminReportView.jsp").forward(request, response);
        } else if (pathInfo.equals("/user-activity")) {
            // Generate user activity report
            int limit = 10;
            String limitStr = request.getParameter("limit");
            if (limitStr != null && !limitStr.isEmpty()) {
                limit = Integer.parseInt(limitStr);
            }

            List<Map<String, Object>> report = reportDAO.getUserActivityReport(startDate, endDate, limit);
            request.setAttribute("report", report);
            request.setAttribute("reportType", "User Activity");
            request.setAttribute("startDate", startDate);
            request.setAttribute("endDate", endDate);
            request.getRequestDispatcher("/View/adminReportView.jsp").forward(request, response);
        } else {
            response.sendRedirect(request.getContextPath() + "/admin/reports");
        }
    }
}