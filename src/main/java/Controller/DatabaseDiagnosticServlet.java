package Controller;

import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import Model.User;
import Model.DirectSQLExecutor;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

@WebServlet("/admin/database-diagnostic")
public class DatabaseDiagnosticServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        HttpSession session = request.getSession();
        User user = (User) session.getAttribute("user");

        // Check if user is logged in and is an admin
        if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
            response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
            return;
        }

        response.setContentType("text/html");
        PrintWriter out = response.getWriter();

        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>Database Diagnostic</title>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 20px; }");
        out.println("h1 { color: #333; }");
        out.println("h2 { color: #666; margin-top: 20px; }");
        out.println("table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }");
        out.println("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
        out.println("th { background-color: #f2f2f2; }");
        out.println("tr:nth-child(even) { background-color: #f9f9f9; }");
        out.println("pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }");
        out.println(".back-link { display: inline-block; margin-bottom: 20px; }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");

        out.println("<a href=\"" + request.getContextPath() + "/View/AdminDashboard.jsp\" class=\"back-link\">&laquo; Back to Dashboard</a>");
        out.println("<h1>Database Diagnostic</h1>");

        // Get all tables
        out.println("<h2>Database Tables</h2>");
        List<String> tables = DirectSQLExecutor.getAllTableNames();

        if (tables.isEmpty()) {
            out.println("<p>No tables found or error retrieving tables.</p>");
        } else {
            out.println("<ul>");
            for (String table : tables) {
                out.println("<li>" + table + "</li>");
            }
            out.println("</ul>");

            // Show table structure for each table
            out.println("<h2>Table Structure</h2>");

            for (String table : tables) {
                out.println("<h3>Table: " + table + "</h3>");
                List<String> columns = DirectSQLExecutor.getTableColumns(table);

                if (columns.isEmpty()) {
                    out.println("<p>No columns found or error retrieving columns.</p>");
                } else {
                    out.println("<table>");
                    out.println("<tr><th>Column Name</th></tr>");

                    for (String column : columns) {
                        out.println("<tr><td>" + column + "</td></tr>");
                    }

                    out.println("</table>");
                }

                // Show sample data
                try (Connection conn = UserDAO.getConnection();
                     PreparedStatement ps = conn.prepareStatement("SELECT * FROM " + table + " LIMIT 5");
                     ResultSet rs = ps.executeQuery()) {

                    out.println("<h4>Sample Data</h4>");
                    out.println("<table>");

                    // Print column headers
                    out.println("<tr>");
                    for (int i = 1; i <= rs.getMetaData().getColumnCount(); i++) {
                        out.println("<th>" + rs.getMetaData().getColumnName(i) + "</th>");
                    }
                    out.println("</tr>");

                    // Print data rows
                    while (rs.next()) {
                        out.println("<tr>");
                        for (int i = 1; i <= rs.getMetaData().getColumnCount(); i++) {
                            out.println("<td>" + (rs.getString(i) != null ? rs.getString(i) : "NULL") + "</td>");
                        }
                        out.println("</tr>");
                    }

                    out.println("</table>");
                } catch (SQLException e) {
                    out.println("<p>Error retrieving sample data: " + e.getMessage() + "</p>");
                }
            }
        }

        out.println("</body>");
        out.println("</html>");
    }
}
