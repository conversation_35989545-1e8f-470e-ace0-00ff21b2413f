package Controller;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

import Model.User;
import Model.DatabaseConnection;
import Model.DirectSQLExecutor;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

@WebServlet("/admin/deleteUser")
public class AdminDeleteUserServlet extends HttpServlet {
    private static final long serialVersionUID = 1L;

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        HttpSession session = request.getSession();
        User user = (User) session.getAttribute("user");

        System.out.println("\n==== AdminDeleteUserServlet: doPost method called ====");

        // Set response content type
        response.setContentType("text/plain;charset=UTF-8");

        // Check if user is logged in and is an admin
        if (user == null) {
            System.out.println("Error: User not logged in");
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.getWriter().write("Unauthorized: You must be logged in");
            return;
        }

        if (!"admin".equalsIgnoreCase(user.getUserType())) {
            System.out.println("Error: User is not an admin. User type: " + user.getUserType());
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.getWriter().write("Unauthorized: Admin access required");
            return;
        }

        String userIdParam = request.getParameter("userId");
        System.out.println("User ID parameter: " + userIdParam);

        if (userIdParam == null || userIdParam.isEmpty()) {
            System.out.println("Error: User ID parameter is missing or empty");
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            response.getWriter().write("User ID is required");
            return;
        }

        try {
            int userId = Integer.parseInt(userIdParam);
            System.out.println("Parsed user ID: " + userId);

            // Don't allow admin to delete themselves
            if (userId == user.getUserId()) {
                System.out.println("Error: Admin attempting to delete their own account");
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("You cannot delete your own admin account");
                return;
            }

            // Use the UserDAO to delete the user
            boolean success = false;
            String errorMessage = "";

            try {
                System.out.println("Attempting to delete user with ID: " + userId + " using UserDAO.deleteUser");
                DAO.UserDAO userDAO = new DAO.UserDAO();
                success = userDAO.deleteUser(userId);
                System.out.println("UserDAO.deleteUser result: " + success);
            } catch (SQLException e) {
                errorMessage = e.getMessage();
                System.err.println("Error deleting user with UserDAO: " + errorMessage);
                e.printStackTrace();

                // If UserDAO method fails, try the direct method
                System.out.println("Attempting to delete user with ID: " + userId + " using deleteUserAndRelatedRecords");
                try {
                    success = deleteUserAndRelatedRecords(userId);
                    System.out.println("deleteUserAndRelatedRecords result: " + success);
                } catch (Exception ex) {
                    errorMessage += " | Direct method error: " + ex.getMessage();
                    System.err.println("Error in deleteUserAndRelatedRecords: " + ex.getMessage());
                    ex.printStackTrace();
                }

                // If that also fails, try DirectSQLExecutor as a last resort
                if (!success) {
                    try {
                        System.out.println("Attempting to delete user with ID: " + userId + " using DirectSQLExecutor.forceDeleteUser");
                        success = DirectSQLExecutor.forceDeleteUser(userId);
                        System.out.println("DirectSQLExecutor.forceDeleteUser result: " + success);
                    } catch (Exception ex) {
                        errorMessage += " | DirectSQLExecutor error: " + ex.getMessage();
                        System.err.println("Error deleting user with DirectSQLExecutor: " + ex.getMessage());
                        ex.printStackTrace();
                    }
                }
            }

            if (success) {
                System.out.println("User deleted successfully");
                response.setStatus(HttpServletResponse.SC_OK);
                response.getWriter().write("User deleted successfully");
            } else {
                System.out.println("Failed to delete user. Error: " + errorMessage);
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("Failed to delete user: " + errorMessage);
            }
        } catch (NumberFormatException e) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            response.getWriter().write("Invalid user ID format");
        } catch (Exception e) {
            e.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("Error: " + e.getMessage());
        }
    }

    private boolean deleteUserAndRelatedRecords(int userId) {
        Connection conn = null;
        try {
            conn = DatabaseConnection.getConnection();
            conn.setAutoCommit(false);

            System.out.println("Starting deletion process for user ID: " + userId);

            // First, check if the user exists
            String checkUserQuery = "SELECT COUNT(*) FROM User WHERE UserId = ?";
            try (PreparedStatement ps = conn.prepareStatement(checkUserQuery)) {
                ps.setInt(1, userId);
                ResultSet rs = ps.executeQuery();
                if (rs.next() && rs.getInt(1) == 0) {
                    System.out.println("User with ID " + userId + " does not exist");
                    return false;
                }
            }

            // Try to delete from specific tables first
            try {
                // Delete from Rental_Details first
                String deleteRentalDetailsQuery = "DELETE FROM Rental_Details WHERE user_id = ?";
                try (PreparedStatement ps = conn.prepareStatement(deleteRentalDetailsQuery)) {
                    ps.setInt(1, userId);
                    int count = ps.executeUpdate();
                    System.out.println("Deleted " + count + " records from Rental_Details");
                }
            } catch (SQLException e) {
                System.out.println("Error deleting from Rental_Details: " + e.getMessage());
                // Continue with other tables
            }

            try {
                // Delete from Rental_Record
                String deleteRentalRecordQuery = "DELETE FROM Rental_Record WHERE user_id = ?";
                try (PreparedStatement ps = conn.prepareStatement(deleteRentalRecordQuery)) {
                    ps.setInt(1, userId);
                    int count = ps.executeUpdate();
                    System.out.println("Deleted " + count + " records from Rental_Record");
                }
            } catch (SQLException e) {
                System.out.println("Error deleting from Rental_Record: " + e.getMessage());
                // Continue with other tables
            }

            // Get all tables in the database
            try {
                Statement stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery("SHOW TABLES");

                // For each table, check if it has a foreign key to User
                while (rs.next()) {
                    String tableName = rs.getString(1);
                    System.out.println("Checking table: " + tableName);

                    if (!"User".equals(tableName)) {
                        // Check if this table has a column referencing User
                        try {
                            ResultSet columns = conn.getMetaData().getColumns(null, null, tableName, "user_id");
                            if (columns.next()) {
                                // This table has a user_id column, delete related records
                                System.out.println("Table " + tableName + " has user_id column, deleting related records");
                                String deleteQuery = "DELETE FROM " + tableName + " WHERE user_id = ?";
                                try (PreparedStatement ps = conn.prepareStatement(deleteQuery)) {
                                    ps.setInt(1, userId);
                                    int count = ps.executeUpdate();
                                    System.out.println("Deleted " + count + " records from " + tableName);
                                }
                            }
                        } catch (SQLException e) {
                            System.out.println("Error checking columns for table " + tableName + ": " + e.getMessage());
                            // Continue with other tables
                        }
                    }
                }
            } catch (SQLException e) {
                System.out.println("Error getting tables: " + e.getMessage());
                // Continue with user deletion
            }

            // Finally, delete the user
            String deleteUserQuery = "DELETE FROM User WHERE UserId = ?";
            try (PreparedStatement ps = conn.prepareStatement(deleteUserQuery)) {
                ps.setInt(1, userId);
                int count = ps.executeUpdate();
                System.out.println("Deleted user with ID: " + userId + ", affected rows: " + count);

                if (count > 0) {
                    conn.commit();
                    return true;
                }
            } catch (SQLException e) {
                System.out.println("Error with standard delete: " + e.getMessage());
                // Try alternative approach
            }

            // If we get here, try a more direct approach
            try {
                // Use a direct SQL statement (case-sensitive)
                String directSql = "DELETE FROM User WHERE UserId = " + userId;
                try (Statement stmt = conn.createStatement()) {
                    int count = stmt.executeUpdate(directSql);
                    System.out.println("Direct SQL delete affected rows: " + count);

                    if (count > 0) {
                        conn.commit();
                        return true;
                    }
                }
            } catch (SQLException e) {
                System.out.println("Error with direct SQL: " + e.getMessage());
            }

            // If we get here, try one more approach with a different case for the table name
            try {
                String alternateSql = "DELETE FROM user WHERE UserId = " + userId;
                try (Statement stmt = conn.createStatement()) {
                    int count = stmt.executeUpdate(alternateSql);
                    System.out.println("Alternate SQL delete affected rows: " + count);

                    if (count > 0) {
                        conn.commit();
                        return true;
                    }
                }
            } catch (SQLException e) {
                System.out.println("Error with alternate SQL: " + e.getMessage());
            }

            // If we get here, all approaches failed
            conn.rollback();
            return false;
        } catch (SQLException e) {
            System.err.println("Error deleting user: " + e.getMessage());
            e.printStackTrace();

            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    System.err.println("Error rolling back transaction: " + ex.getMessage());
                }
            }

            return false;
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    conn.close();
                } catch (SQLException e) {
                    System.err.println("Error closing connection: " + e.getMessage());
                }
            }
        }
    }
}
