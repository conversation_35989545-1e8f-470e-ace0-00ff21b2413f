package Controller;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import com.google.gson.Gson;

import Model.User;
import Utils.PasswordHash;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

public class ProfileUpdateServlet extends HttpServlet {
    private static final Logger logger = Logger.getLogger(ProfileUpdateServlet.class.getName());
    private static final Gson gson = new Gson();

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        response.setContentType("application/json");
        PrintWriter out = response.getWriter();
        Map<String, Object> jsonResponse = new HashMap<>();

        try {
            HttpSession session = request.getSession();
            User currentUser = (User) session.getAttribute("user");

            if (currentUser == null) {
                logger.warning("User not logged in");
                jsonResponse.put("success", false);
                jsonResponse.put("message", "User not logged in");
                out.print(gson.toJson(jsonResponse));
                return;
            }

            // Get form parameters
            String username = request.getParameter("username");
            String email = request.getParameter("email");
            String currentPassword = request.getParameter("currentPassword");
            String newPassword = request.getParameter("newPassword");

            if (username == null || email == null) {
                logger.warning("Missing required parameters");
                jsonResponse.put("success", false);
                jsonResponse.put("message", "Missing required parameters");
                out.print(gson.toJson(jsonResponse));
                return;
            }

            logger.info("Updating profile for user: " + currentUser.getUserId());
            logger.info("New username: " + username);
            logger.info("New email: " + email);

            // Check if email is being changed and if it already exists
            if (!email.equals(currentUser.getEmail()) && UserDAO.checkEmail(email)) {
                logger.warning("Email already exists: " + email);
                jsonResponse.put("success", false);
                jsonResponse.put("message", "Email already exists");
                out.print(gson.toJson(jsonResponse));
                return;
            }

            // Handle password change if new password is provided
            if (newPassword != null && !newPassword.isEmpty()) {
                if (currentPassword == null || currentPassword.isEmpty()) {
                    logger.warning("Current password is required for password change");
                    jsonResponse.put("success", false);
                    jsonResponse.put("message", "Current password is required");
                    out.print(gson.toJson(jsonResponse));
                    return;
                }

                // Verify current password
                if (!UserDAO.verifyPassword(currentUser.getUserId(), currentPassword)) {
                    logger.warning("Current password is incorrect");
                    jsonResponse.put("success", false);
                    jsonResponse.put("message", "Current password is incorrect");
                    out.print(gson.toJson(jsonResponse));
                    return;
                }

                // Hash and set new password
                String hashedPassword = PasswordHash.hash(newPassword);
                currentUser.setPassword(hashedPassword);
            }

            // Update user information
            currentUser.setUserName(username);
            currentUser.setEmail(email);

            // Update in database
            if (UserDAO.updateUserProfile(currentUser)) {
                logger.info("Profile updated successfully for user: " + currentUser.getUserId());
                // Update session
                session.setAttribute("user", currentUser);
                jsonResponse.put("success", true);
                jsonResponse.put("message", "Profile updated successfully");
            } else {
                logger.warning("Failed to update profile for user: " + currentUser.getUserId());
                jsonResponse.put("success", false);
                jsonResponse.put("message", "Failed to update profile");
            }

            out.print(gson.toJson(jsonResponse));

        } catch (Exception e) {
            logger.severe("Error updating profile: " + e.getMessage());
            jsonResponse.put("success", false);
            jsonResponse.put("message", "An error occurred while updating profile");
            out.print(gson.toJson(jsonResponse));
        }
    }
}