package Filter;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import Model.DatabaseConnection;
import Utils.PasswordHash;

public class role
{
    //Admin Email and Password
    public static boolean checkAdmin(String email, String password) throws SQLException
    {
        boolean adminDetail = false;

        try (Connection con = getConnection())
        {
            String checkAdminQuerry = "SELECT email, password FROM user WHERE UserId = 1";

            PreparedStatement CheckAdminPS = con.prepareStatement(checkAdminQuerry);

            ResultSet AdminDetail = CheckAdminPS.executeQuery();

            if(AdminDetail.next())
            {
                String adminEmail = AdminDetail.getString("email");
                String adminPassword = AdminDetail.getString("password");

                if(adminEmail.equals(email) && PasswordHash.check(password, adminPassword))
                {
                    adminDetail = true;
                }
            }
        }
        return adminDetail;
    }
}
