package DAO;

/*
 * This class is temporarily commented out until the Course table is created in the database
 */

/*
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import Model.Course;
import Model.DatabaseConnection;

public class CourseDAO {
    private Connection connection;

    public CourseDAO() {
        try {
            connection = DatabaseConnection.getConnection();
            if (connection == null) {
                System.err.println("Failed to get database connection");
                throw new SQLException("Database connection is null");
            }
        } catch (Exception e) {
            System.err.println("Error initializing CourseDAO: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public List<Course> getAllCourses() {
        List<Course> courses = new ArrayList<>();
        String query = "SELECT c.*, u.FullName as teacher_name FROM Course c " +
                       "LEFT JOIN User u ON c.teacher_id = u.UserId " +
                       "ORDER BY c.course_code";

        try {
            // Ensure connection is valid
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ResultSet rs = ps.executeQuery();

                while (rs.next()) {
                    Course course = mapResultSetToCourse(rs);
                    courses.add(course);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting all courses: " + e.getMessage());
            e.printStackTrace();
        }
        return courses;
    }

    public Course getCourseById(int courseId) {
        String query = "SELECT c.*, u.FullName as teacher_name FROM Course c " +
                       "LEFT JOIN User u ON c.teacher_id = u.UserId " +
                       "WHERE c.course_id = ?";

        try {
            // Ensure connection is valid
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setInt(1, courseId);
                ResultSet rs = ps.executeQuery();

                if (rs.next()) {
                    return mapResultSetToCourse(rs);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting course by ID: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    public Course getCourseByCourseCode(String courseCode) {
        String query = "SELECT c.*, u.FullName as teacher_name FROM Course c " +
                       "LEFT JOIN User u ON c.teacher_id = u.UserId " +
                       "WHERE c.course_code = ?";

        try {
            // Ensure connection is valid
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setString(1, courseCode);
                ResultSet rs = ps.executeQuery();

                if (rs.next()) {
                    return mapResultSetToCourse(rs);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting course by course code: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    public List<Course> getCoursesByDepartment(String department) {
        List<Course> courses = new ArrayList<>();
        String query = "SELECT c.*, u.FullName as teacher_name FROM Course c " +
                       "LEFT JOIN User u ON c.teacher_id = u.UserId " +
                       "WHERE c.department = ? " +
                       "ORDER BY c.course_code";

        try {
            // Ensure connection is valid
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setString(1, department);
                ResultSet rs = ps.executeQuery();

                while (rs.next()) {
                    Course course = mapResultSetToCourse(rs);
                    courses.add(course);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting courses by department: " + e.getMessage());
            e.printStackTrace();
        }
        return courses;
    }

    public List<Course> getCoursesByTeacher(int teacherId) {
        List<Course> courses = new ArrayList<>();
        String query = "SELECT c.*, u.FullName as teacher_name FROM Course c " +
                       "LEFT JOIN User u ON c.teacher_id = u.UserId " +
                       "WHERE c.teacher_id = ? " +
                       "ORDER BY c.course_code";

        try {
            // Ensure connection is valid
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setInt(1, teacherId);
                ResultSet rs = ps.executeQuery();

                while (rs.next()) {
                    Course course = mapResultSetToCourse(rs);
                    courses.add(course);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting courses by teacher: " + e.getMessage());
            e.printStackTrace();
        }
        return courses;
    }

    public List<Course> getCoursesByStudent(int studentId) {
        List<Course> courses = new ArrayList<>();
        String query = "SELECT c.*, u.FullName as teacher_name FROM Course c " +
                       "JOIN Enrollment e ON c.course_id = e.course_id " +
                       "LEFT JOIN User u ON c.teacher_id = u.UserId " +
                       "WHERE e.student_id = ? " +
                       "ORDER BY c.course_code";

        try {
            // Ensure connection is valid
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setInt(1, studentId);
                ResultSet rs = ps.executeQuery();

                while (rs.next()) {
                    Course course = mapResultSetToCourse(rs);
                    courses.add(course);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting courses by student: " + e.getMessage());
            e.printStackTrace();
        }
        return courses;
    }

    public boolean addCourse(Course course) {
        String query = "INSERT INTO Course (course_code, course_name, description, credits, " +
                       "department, teacher_id, semester, is_active) " +
                       "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

        try {
            // Ensure connection is valid
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setString(1, course.getCourseCode());
                ps.setString(2, course.getCourseName());
                ps.setString(3, course.getDescription());
                ps.setInt(4, course.getCredits());
                ps.setString(5, course.getDepartment());
                ps.setInt(6, course.getTeacherId());
                ps.setInt(7, course.getSemester());
                ps.setBoolean(8, course.isActive());

                int result = ps.executeUpdate();
                return result > 0;
            }
        } catch (SQLException e) {
            System.err.println("Error adding course: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    public boolean updateCourse(Course course) {
        String query = "UPDATE Course SET course_code = ?, course_name = ?, description = ?, " +
                       "credits = ?, department = ?, teacher_id = ?, semester = ?, is_active = ? " +
                       "WHERE course_id = ?";

        try {
            // Ensure connection is valid
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setString(1, course.getCourseCode());
                ps.setString(2, course.getCourseName());
                ps.setString(3, course.getDescription());
                ps.setInt(4, course.getCredits());
                ps.setString(5, course.getDepartment());
                ps.setInt(6, course.getTeacherId());
                ps.setInt(7, course.getSemester());
                ps.setBoolean(8, course.isActive());
                ps.setInt(9, course.getCourseId());

                int result = ps.executeUpdate();
                return result > 0;
            }
        } catch (SQLException e) {
            System.err.println("Error updating course: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    public boolean deleteCourse(int courseId) {
        String query = "DELETE FROM Course WHERE course_id = ?";

        try {
            // Ensure connection is valid
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setInt(1, courseId);

                int result = ps.executeUpdate();
                return result > 0;
            }
        } catch (SQLException e) {
            System.err.println("Error deleting course: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    private Course mapResultSetToCourse(ResultSet rs) throws SQLException {
        Course course = new Course();
        course.setCourseId(rs.getInt("course_id"));
        course.setCourseCode(rs.getString("course_code"));
        course.setCourseName(rs.getString("course_name"));
        course.setDescription(rs.getString("description"));
        course.setCredits(rs.getInt("credits"));
        course.setDepartment(rs.getString("department"));
        course.setTeacherId(rs.getInt("teacher_id"));
        course.setTeacherName(rs.getString("teacher_name"));
        course.setSemester(rs.getInt("semester"));
        course.setActive(rs.getBoolean("is_active"));
        course.setCreatedAt(rs.getTimestamp("created_at"));
        course.setUpdatedAt(rs.getTimestamp("updated_at"));
        return course;
    }
}
*/
