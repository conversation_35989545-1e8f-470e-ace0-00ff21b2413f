package DAO;

import Model.Report;
import Util.DatabaseConnection;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ReportDAO {
    private Connection connection;

    public ReportDAO() {
        try {
            this.connection = DatabaseConnection.getConnection();
        } catch (SQLException e) {
            System.err.println("Error connecting to database: " + e.getMessage());
        }
    }

    // Get rental activity report
    public Map<String, Object> getRentalActivityReport(LocalDate startDate, LocalDate endDate) {
        Map<String, Object> report = new HashMap<>();
        
        String query = "SELECT COUNT(*) as total_rentals, " +
                      "SUM(CASE WHEN rental_status = 'ACTIVE' THEN 1 ELSE 0 END) as active_rentals, " +
                      "SUM(CASE WHEN rental_status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_rentals, " +
                      "SUM(CASE WHEN rental_status = 'PENDING' THEN 1 ELSE 0 END) as pending_rentals, " +
                      "SUM(rental_amount) as total_revenue " +
                      "FROM Rental_Details " +
                      "WHERE rental_date BETWEEN ? AND ?";

        try {
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setDate(1, java.sql.Date.valueOf(startDate));
                ps.setDate(2, java.sql.Date.valueOf(endDate));
                
                ResultSet rs = ps.executeQuery();
                
                if (rs.next()) {
                    report.put("totalRentals", rs.getInt("total_rentals"));
                    report.put("activeRentals", rs.getInt("active_rentals"));
                    report.put("completedRentals", rs.getInt("completed_rentals"));
                    report.put("pendingRentals", rs.getInt("pending_rentals"));
                    report.put("totalRevenue", rs.getDouble("total_revenue"));
                }
            }
        } catch (SQLException e) {
            System.err.println("Error generating rental activity report: " + e.getMessage());
        }
        
        return report;
    }
    
    // Get popular books report
    public List<Map<String, Object>> getPopularBooksReport(LocalDate startDate, LocalDate endDate, int limit) {
        List<Map<String, Object>> report = new ArrayList<>();
        
        String query = "SELECT b.book_id, b.title, b.author, COUNT(rd.book_id) as rental_count, " +
                      "SUM(rd.rental_amount) as revenue " +
                      "FROM Book b " +
                      "JOIN Rental_Details rd ON b.book_id = rd.book_id " +
                      "WHERE rd.rental_date BETWEEN ? AND ? " +
                      "GROUP BY b.book_id " +
                      "ORDER BY rental_count DESC " +
                      "LIMIT ?";

        try {
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setDate(1, java.sql.Date.valueOf(startDate));
                ps.setDate(2, java.sql.Date.valueOf(endDate));
                ps.setInt(3, limit);
                
                ResultSet rs = ps.executeQuery();
                
                while (rs.next()) {
                    Map<String, Object> book = new HashMap<>();
                    book.put("bookId", rs.getInt("book_id"));
                    book.put("title", rs.getString("title"));
                    book.put("author", rs.getString("author"));
                    book.put("rentalCount", rs.getInt("rental_count"));
                    book.put("revenue", rs.getDouble("revenue"));
                    report.add(book);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error generating popular books report: " + e.getMessage());
        }
        
        return report;
    }
    
    // Get user activity report
    public List<Map<String, Object>> getUserActivityReport(LocalDate startDate, LocalDate endDate, int limit) {
        List<Map<String, Object>> report = new ArrayList<>();
        
        String query = "SELECT u.UserId, u.Username, u.Email, COUNT(rd.user_id) as rental_count, " +
                      "SUM(rd.rental_amount) as total_spent " +
                      "FROM User u " +
                      "JOIN Rental_Details rd ON u.UserId = rd.user_id " +
                      "WHERE rd.rental_date BETWEEN ? AND ? " +
                      "GROUP BY u.UserId " +
                      "ORDER BY rental_count DESC " +
                      "LIMIT ?";

        try {
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setDate(1, java.sql.Date.valueOf(startDate));
                ps.setDate(2, java.sql.Date.valueOf(endDate));
                ps.setInt(3, limit);
                
                ResultSet rs = ps.executeQuery();
                
                while (rs.next()) {
                    Map<String, Object> user = new HashMap<>();
                    user.put("userId", rs.getInt("UserId"));
                    user.put("username", rs.getString("Username"));
                    user.put("email", rs.getString("Email"));
                    user.put("rentalCount", rs.getInt("rental_count"));
                    user.put("totalSpent", rs.getDouble("total_spent"));
                    report.add(user);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error generating user activity report: " + e.getMessage());
        }
        
        return report;
    }
}