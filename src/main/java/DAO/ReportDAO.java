package DAO;

import Model.Report;
import Model.DatabaseConnection;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ReportDAO {
    private Connection connection;

    public ReportDAO() {
        try {
            this.connection = DatabaseConnection.getConnection();
        } catch (SQLException e) {
            System.err.println("Error connecting to database: " + e.getMessage());
        }
    }

    // Get rental activity report
    public Map<String, Object> getRentalActivityReport(LocalDate startDate, LocalDate endDate) {
        Map<String, Object> report = new HashMap<>();

        // Initialize with default values in case of database error
        report.put("totalRentals", 0);
        report.put("activeRentals", 0);
        report.put("completedRentals", 0);
        report.put("pendingRentals", 0);
        report.put("totalRevenue", 0.0);

        String query = "SELECT COUNT(*) as total_rentals, " +
                      "SUM(CASE WHEN rental_status = 'ACTIVE' THEN 1 ELSE 0 END) as active_rentals, " +
                      "SUM(CASE WHEN rental_status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_rentals, " +
                      "SUM(CASE WHEN rental_status = 'PENDING' THEN 1 ELSE 0 END) as pending_rentals, " +
                      "SUM(rental_amount) as total_revenue " +
                      "FROM Rental_Details " +
                      "WHERE rental_date BETWEEN ? AND ?";

        try {
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setDate(1, java.sql.Date.valueOf(startDate));
                ps.setDate(2, java.sql.Date.valueOf(endDate));

                System.out.println("Executing rental activity query for period: " + startDate + " to " + endDate);
                ResultSet rs = ps.executeQuery();

                if (rs.next()) {
                    report.put("totalRentals", rs.getInt("total_rentals"));
                    report.put("activeRentals", rs.getInt("active_rentals"));
                    report.put("completedRentals", rs.getInt("completed_rentals"));
                    report.put("pendingRentals", rs.getInt("pending_rentals"));
                    report.put("totalRevenue", rs.getDouble("total_revenue"));
                    System.out.println("Report data retrieved successfully");
                } else {
                    System.out.println("No rental data found for the period");
                }
            }
        } catch (SQLException e) {
            System.err.println("Error generating rental activity report: " + e.getMessage());
            e.printStackTrace();
        }

        return report;
    }

    // Get popular books report
    public List<Map<String, Object>> getPopularBooksReport(LocalDate startDate, LocalDate endDate, int limit) {
        List<Map<String, Object>> report = new ArrayList<>();

        // If no books found, add a sample entry for demonstration
        if (startDate == null || endDate == null) {
            Map<String, Object> sampleBook = new HashMap<>();
            sampleBook.put("bookId", 1);
            sampleBook.put("title", "Sample Book");
            sampleBook.put("author", "Sample Author");
            sampleBook.put("rentalCount", 5);
            sampleBook.put("revenue", 500.0);
            report.add(sampleBook);
            return report;
        }

        String query = "SELECT b.book_id, b.title, b.author, COUNT(rd.book_id) as rental_count, " +
                      "SUM(rd.rental_amount) as revenue " +
                      "FROM Book b " +
                      "JOIN Rental_Details rd ON b.book_id = rd.book_id " +
                      "WHERE rd.rental_date BETWEEN ? AND ? " +
                      "GROUP BY b.book_id " +
                      "ORDER BY rental_count DESC " +
                      "LIMIT ?";

        try {
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setDate(1, java.sql.Date.valueOf(startDate));
                ps.setDate(2, java.sql.Date.valueOf(endDate));
                ps.setInt(3, limit);

                System.out.println("Executing popular books query for period: " + startDate + " to " + endDate + ", limit: " + limit);
                ResultSet rs = ps.executeQuery();

                boolean hasData = false;
                while (rs.next()) {
                    hasData = true;
                    Map<String, Object> book = new HashMap<>();
                    book.put("bookId", rs.getInt("book_id"));
                    book.put("title", rs.getString("title"));
                    book.put("author", rs.getString("author"));
                    book.put("rentalCount", rs.getInt("rental_count"));
                    book.put("revenue", rs.getDouble("revenue"));
                    report.add(book);
                }

                if (!hasData) {
                    System.out.println("No popular books data found for the period");
                    // Add sample data if no results found
                    Map<String, Object> sampleBook = new HashMap<>();
                    sampleBook.put("bookId", 1);
                    sampleBook.put("title", "No rental data available");
                    sampleBook.put("author", "Please try a different date range");
                    sampleBook.put("rentalCount", 0);
                    sampleBook.put("revenue", 0.0);
                    report.add(sampleBook);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error generating popular books report: " + e.getMessage());
            e.printStackTrace();

            // Add error message as a book entry
            Map<String, Object> errorBook = new HashMap<>();
            errorBook.put("bookId", 0);
            errorBook.put("title", "Error retrieving data");
            errorBook.put("author", "Database error: " + e.getMessage());
            errorBook.put("rentalCount", 0);
            errorBook.put("revenue", 0.0);
            report.add(errorBook);
        }

        return report;
    }

    // Get user activity report
    public List<Map<String, Object>> getUserActivityReport(LocalDate startDate, LocalDate endDate, int limit) {
        List<Map<String, Object>> report = new ArrayList<>();

        // If no users found, add a sample entry for demonstration
        if (startDate == null || endDate == null) {
            Map<String, Object> sampleUser = new HashMap<>();
            sampleUser.put("userId", 1);
            sampleUser.put("username", "Sample User");
            sampleUser.put("email", "<EMAIL>");
            sampleUser.put("rentalCount", 3);
            sampleUser.put("totalSpent", 300.0);
            report.add(sampleUser);
            return report;
        }

        String query = "SELECT u.UserId, u.Username, u.Email, COUNT(rd.user_id) as rental_count, " +
                      "SUM(rd.rental_amount) as total_spent " +
                      "FROM User u " +
                      "JOIN Rental_Details rd ON u.UserId = rd.user_id " +
                      "WHERE rd.rental_date BETWEEN ? AND ? " +
                      "GROUP BY u.UserId " +
                      "ORDER BY rental_count DESC " +
                      "LIMIT ?";

        try {
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setDate(1, java.sql.Date.valueOf(startDate));
                ps.setDate(2, java.sql.Date.valueOf(endDate));
                ps.setInt(3, limit);

                System.out.println("Executing user activity query for period: " + startDate + " to " + endDate + ", limit: " + limit);
                ResultSet rs = ps.executeQuery();

                boolean hasData = false;
                while (rs.next()) {
                    hasData = true;
                    Map<String, Object> user = new HashMap<>();
                    user.put("userId", rs.getInt("UserId"));
                    user.put("username", rs.getString("Username"));
                    user.put("email", rs.getString("Email"));
                    user.put("rentalCount", rs.getInt("rental_count"));
                    user.put("totalSpent", rs.getDouble("total_spent"));
                    report.add(user);
                }

                if (!hasData) {
                    System.out.println("No user activity data found for the period");
                    // Add sample data if no results found
                    Map<String, Object> sampleUser = new HashMap<>();
                    sampleUser.put("userId", 0);
                    sampleUser.put("username", "No user data available");
                    sampleUser.put("email", "Please try a different date range");
                    sampleUser.put("rentalCount", 0);
                    sampleUser.put("totalSpent", 0.0);
                    report.add(sampleUser);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error generating user activity report: " + e.getMessage());
            e.printStackTrace();

            // Add error message as a user entry
            Map<String, Object> errorUser = new HashMap<>();
            errorUser.put("userId", 0);
            errorUser.put("username", "Error retrieving data");
            errorUser.put("email", "Database error: " + e.getMessage());
            errorUser.put("rentalCount", 0);
            errorUser.put("totalSpent", 0.0);
            report.add(errorUser);
        }

        return report;
    }
}