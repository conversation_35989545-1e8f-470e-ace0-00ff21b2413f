package DAO;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import Model.DatabaseConnection;
import Model.User;

public class UserDAO {
    private Connection connection;

    public UserDAO() {
        try {
            connection = DatabaseConnection.getConnection();
            if (connection == null) {
                System.err.println("Failed to get database connection");
                throw new SQLException("Database connection is null");
            }
        } catch (Exception e) {
            System.err.println("Error initializing UserDAO: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // Get all users
    public List<User> getAllUsers() throws SQLException {
        List<User> users = new ArrayList<>();
        String query = "SELECT * FROM User ORDER BY UserId";

        try {
            // Ensure connection is valid
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ResultSet rs = ps.executeQuery();

                while (rs.next()) {
                    User user = new User();
                    user.setUserId(rs.getInt("UserId"));
                    user.setUserName(rs.getString("Username"));
                    user.setEmail(rs.getString("Email"));
                    user.setFullName(rs.getString("FullName"));
                    user.setUserType(rs.getString("UserType"));
                    users.add(user);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting all users: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }

        return users;
    }

    // Get user by ID
    public User getUserById(int userId) throws SQLException {
        String query = "SELECT * FROM User WHERE UserId = ?";

        try {
            // Ensure connection is valid
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setInt(1, userId);
                ResultSet rs = ps.executeQuery();

                if (rs.next()) {
                    User user = new User();
                    user.setUserId(rs.getInt("UserId"));
                    user.setUserName(rs.getString("Username"));
                    user.setEmail(rs.getString("Email"));
                    user.setPassword(rs.getString("Password"));
                    user.setFullName(rs.getString("FullName"));
                    user.setUserType(rs.getString("UserType"));
                    return user;
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting user by ID: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
        return null;
    }

    // Authenticate user
    public User authenticateUser(String email, String password) throws SQLException {
        String query = "SELECT * FROM User WHERE Email = ? AND Password = ?";

        try {
            // Ensure connection is valid
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setString(1, email);
                ps.setString(2, password);
                ResultSet rs = ps.executeQuery();

                if (rs.next()) {
                    String userType = rs.getString("UserType");
                    if (userType == null) userType = "user";

                    User user = new User(
                        rs.getInt("UserId"),
                        0, // RecordId
                        0, // BookId
                        0, // RentalPeriod
                        0, // PlaylistId
                        "", // Title
                        "", // Author
                        "", // Genre
                        "", // Description
                        rs.getString("Username"),
                        rs.getString("Email"),
                        rs.getString("Password"),
                        userType, // UserType
                        null, // RentalDate
                        null, // ReturnDate
                        null, // DateAdded
                        true // Availability
                    );

                    user.setFullName(rs.getString("FullName"));
                    return user;
                }
            }
        } catch (SQLException e) {
            System.err.println("Error authenticating user: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
        return null;
    }

    // Add new user
    public boolean addUser(User user) {
        String query = "INSERT INTO User (Username, Email, Password, FullName, UserType) VALUES (?, ?, ?, ?, ?)";

        try {
            // Ensure connection is valid
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setString(1, user.getUserName());
                ps.setString(2, user.getEmail());
                ps.setString(3, user.getPassword());
                ps.setString(4, user.getFullName());
                ps.setString(5, user.getUserType());

                int result = ps.executeUpdate();
                return result > 0;
            }
        } catch (SQLException e) {
            System.err.println("Error adding user: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    // Update user
    public boolean updateUser(User user) {
        String query = "UPDATE User SET Username = ?, Email = ?, FullName = ?, UserType = ? WHERE UserId = ?";

        try {
            // Ensure connection is valid
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setString(1, user.getUserName());
                ps.setString(2, user.getEmail());
                ps.setString(3, user.getFullName());
                ps.setString(4, user.getUserType());
                ps.setInt(5, user.getUserId());

                int result = ps.executeUpdate();
                return result > 0;
            }
        } catch (SQLException e) {
            System.err.println("Error updating user: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    // Delete user
    public boolean deleteUser(int userId) {
        String query = "DELETE FROM User WHERE UserId = ?";

        try {
            // Ensure connection is valid
            if (connection == null || connection.isClosed()) {
                connection = DatabaseConnection.getConnection();
            }

            try (PreparedStatement ps = connection.prepareStatement(query)) {
                ps.setInt(1, userId);

                int result = ps.executeUpdate();
                return result > 0;
            }
        } catch (SQLException e) {
            System.err.println("Error deleting user: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    // Get total users count
    public static int getTotalUsers() throws SQLException {
        String query = "SELECT COUNT(*) as total FROM User";

        try (Connection con = DatabaseConnection.getConnection();
             PreparedStatement ps = con.prepareStatement(query)) {

            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                return rs.getInt("total");
            }
        } catch (SQLException e) {
            System.err.println("Error getting total users: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
        return 0;
    }

    // Get recent users
    public static List<User> getRecentUsers(int limit) throws SQLException {
        List<User> users = new ArrayList<>();
        String query = "SELECT * FROM User ORDER BY created_at DESC LIMIT ?";

        try (Connection con = DatabaseConnection.getConnection();
             PreparedStatement ps = con.prepareStatement(query)) {

            ps.setInt(1, limit);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                User user = new User();
                user.setUserId(rs.getInt("UserId"));
                user.setUserName(rs.getString("Username"));
                user.setEmail(rs.getString("Email"));
                user.setFullName(rs.getString("FullName"));
                user.setUserType(rs.getString("UserType"));
                users.add(user);
            }
        } catch (SQLException e) {
            System.err.println("Error getting recent users: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }

        return users;
    }

    // Check if email already exists
    public static boolean checkEmail(String email) throws SQLException {
        String query = "SELECT COUNT(*) FROM User WHERE Email = ?";

        try (Connection con = DatabaseConnection.getConnection();
             PreparedStatement ps = con.prepareStatement(query)) {

            ps.setString(1, email);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return rs.getInt(1) > 0; // Returns true if email exists
            }
        } catch (SQLException e) {
            System.err.println("Error checking email: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
        return false;
    }

    // Update user profile (static method for compatibility)
    public static boolean updateUserProfile(User user) throws SQLException {
        UserDAO userDAO = new UserDAO();
        return userDAO.updateUser(user);
    }

    // Register new user (static method for compatibility)
    public static void register(User user) throws SQLException {
        UserDAO userDAO = new UserDAO();
        boolean success = userDAO.addUser(user);
        if (!success) {
            throw new SQLException("Failed to register user");
        }
    }
}
