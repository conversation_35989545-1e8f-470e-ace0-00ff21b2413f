package Model;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import Utils.PasswordHash;

public class UserDAO
{
    // Method to establish Database connection using DatabaseConnection class
    public static Connection getConnection() throws SQLException
    {
        return DatabaseConnection.getConnection();
    };

    // Get all users (for admin)
    public static List<User> getAllUsers() throws SQLException {
        List<User> users = new ArrayList<>();

        try (Connection con = getConnection()) {
            // Use the correct case for table and column names
            String query = "SELECT * FROM User ORDER BY UserId";

            try (PreparedStatement ps = con.prepareStatement(query)) {
                ResultSet rs = ps.executeQuery();

                while (rs.next()) {
                    User user = new User();
                    user.setUserId(rs.getInt("UserId"));
                    user.setUserName(rs.getString("Username"));
                    user.setEmail(rs.getString("Email"));
                    user.setFullName(rs.getString("FullName"));
                    user.setUserType(rs.getString("UserType"));
                    // Add other fields as needed

                    users.add(user);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting all users: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }

        return users;
    }

    // Get user by ID (for admin)
    public static User getUserById(int userId) throws SQLException {
        try (Connection con = getConnection()) {
            // Use the correct case for table and column names
            String query = "SELECT * FROM User WHERE UserId = ?";

            try (PreparedStatement ps = con.prepareStatement(query)) {
                ps.setInt(1, userId);
                ResultSet rs = ps.executeQuery();

                if (rs.next()) {
                    User user = new User();
                    user.setUserId(rs.getInt("UserId"));
                    user.setUserName(rs.getString("Username"));
                    user.setEmail(rs.getString("Email"));
                    user.setFullName(rs.getString("FullName"));
                    user.setUserType(rs.getString("UserType"));
                    // Add other fields as needed

                    return user;
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting user by ID: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }

        return null;
    }

    // Update user (for admin)
    public static boolean updateUser(User user) throws SQLException {
        try (Connection con = getConnection()) {
            // Use the correct case for table and column names
            String query = "UPDATE User SET Username = ?, Email = ?, FullName = ?, UserType = ? WHERE UserId = ?";

            try (PreparedStatement ps = con.prepareStatement(query)) {
                ps.setString(1, user.getUserName());
                ps.setString(2, user.getEmail());
                ps.setString(3, user.getFullName());
                ps.setString(4, user.getUserType());
                ps.setInt(5, user.getUserId());

                int result = ps.executeUpdate();
                return result > 0;
            }
        } catch (SQLException e) {
            System.err.println("Error updating user: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    // Delete user (for admin)
    public static boolean deleteUser(int userId) throws SQLException {
        Connection con = null;
        try {
            con = getConnection();
            con.setAutoCommit(false); // Start transaction

            System.out.println("Starting deletion process for user ID: " + userId);

            // First, check if the user exists
            String checkUserQuery = "SELECT COUNT(*) FROM User WHERE UserId = ?";
            try (PreparedStatement checkUserPs = con.prepareStatement(checkUserQuery)) {
                checkUserPs.setInt(1, userId);
                ResultSet rs = checkUserPs.executeQuery();
                if (rs.next() && rs.getInt(1) == 0) {
                    System.out.println("User with ID " + userId + " does not exist");
                    return false;
                }
                System.out.println("User with ID " + userId + " exists, proceeding with deletion");
            }

            // Try a direct SQL approach with cascading deletes
            try {
                // First, delete any rental details associated with the user
                String deleteRentalDetailsQuery = "DELETE FROM Rental_Details WHERE user_id = ?";
                try (PreparedStatement ps = con.prepareStatement(deleteRentalDetailsQuery)) {
                    ps.setInt(1, userId);
                    int count = ps.executeUpdate();
                    System.out.println("Deleted " + count + " rental details for user ID: " + userId);
                } catch (SQLException e) {
                    System.err.println("Error deleting rental details: " + e.getMessage());
                    // Continue with other deletions
                }

                // Then, delete any rental records associated with the user
                String deleteRentalRecordsQuery = "DELETE FROM Rental_Record WHERE user_id = ?";
                try (PreparedStatement ps = con.prepareStatement(deleteRentalRecordsQuery)) {
                    ps.setInt(1, userId);
                    int count = ps.executeUpdate();
                    System.out.println("Deleted " + count + " rental records for user ID: " + userId);
                } catch (SQLException e) {
                    System.err.println("Error deleting rental records: " + e.getMessage());
                    // Continue with user deletion
                }

                // Finally, delete the user
                String deleteUserQuery = "DELETE FROM User WHERE UserId = ?";
                try (PreparedStatement ps = con.prepareStatement(deleteUserQuery)) {
                    ps.setInt(1, userId);
                    int count = ps.executeUpdate();
                    System.out.println("Deleted user with ID: " + userId + ", affected rows: " + count);

                    if (count > 0) {
                        con.commit();
                        System.out.println("Successfully deleted user with ID: " + userId);
                        return true;
                    } else {
                        System.out.println("No rows affected when deleting user with ID: " + userId);
                        // Try alternative approaches before giving up
                    }
                } catch (SQLException e) {
                    System.err.println("Error deleting user with prepared statement: " + e.getMessage());
                    // Continue with alternative approaches
                }

                // Try direct SQL with case-sensitive table name
                System.out.println("Trying direct SQL approach for user ID: " + userId);
                String directSql = "DELETE FROM User WHERE UserId = " + userId;
                try (Statement stmt = con.createStatement()) {
                    int count = stmt.executeUpdate(directSql);
                    System.out.println("Direct SQL delete affected rows: " + count);

                    if (count > 0) {
                        con.commit();
                        System.out.println("Successfully deleted user with direct SQL");
                        return true;
                    } else {
                        System.out.println("No rows affected with direct SQL");
                    }
                } catch (SQLException e) {
                    System.err.println("Error with direct SQL: " + e.getMessage());
                }

                // Try with lowercase table name
                System.out.println("Trying with lowercase table name for user ID: " + userId);
                String lowerCaseSql = "DELETE FROM user WHERE UserId = " + userId;
                try (Statement stmt = con.createStatement()) {
                    int count = stmt.executeUpdate(lowerCaseSql);
                    System.out.println("Lowercase SQL delete affected rows: " + count);

                    if (count > 0) {
                        con.commit();
                        System.out.println("Successfully deleted user with lowercase SQL");
                        return true;
                    } else {
                        System.out.println("No rows affected with lowercase SQL");
                        con.rollback();
                        return false;
                    }
                } catch (SQLException e) {
                    System.err.println("Error with lowercase SQL: " + e.getMessage());
                    con.rollback();
                    throw e;
                }
            } catch (SQLException e) {
                System.err.println("Error in direct SQL approach: " + e.getMessage());
                e.printStackTrace();
                con.rollback();
                throw e;
            }
        } catch (SQLException e) {
            if (con != null) {
                try {
                    con.rollback(); // Rollback transaction on error
                } catch (SQLException ex) {
                    System.err.println("Error rolling back transaction: " + ex.getMessage());
                }
            }
            System.err.println("Error deleting user: " + e.getMessage());
            e.printStackTrace();
            throw e;
        } finally {
            if (con != null) {
                try {
                    con.setAutoCommit(true); // Reset auto-commit
                    con.close();
                } catch (SQLException e) {
                    System.err.println("Error closing connection: " + e.getMessage());
                }
            }
        }
    }

    // Alternative method to delete user using a direct SQL statement
    public static boolean deleteUserDirectSQL(int userId) throws SQLException {
        try (Connection con = getConnection()) {
            // First, try to delete any related records
            try {
                // Delete rental details
                String sql = "DELETE FROM Rental_Details WHERE user_id = " + userId;
                try (PreparedStatement ps = con.prepareStatement(sql)) {
                    ps.executeUpdate();
                }

                // Delete rental records
                sql = "DELETE FROM Rental_Record WHERE user_id = " + userId;
                try (PreparedStatement ps = con.prepareStatement(sql)) {
                    ps.executeUpdate();
                }
            } catch (SQLException e) {
                System.err.println("Error deleting related records: " + e.getMessage());
                // Continue with user deletion even if related records deletion fails
            }

            // Now delete the user
            String sql = "DELETE FROM User WHERE UserId = " + userId;
            try (PreparedStatement ps = con.prepareStatement(sql)) {
                int count = ps.executeUpdate();
                return count > 0;
            }
        } catch (SQLException e) {
            System.err.println("Error in deleteUserDirectSQL: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    //    Function 1: Register new user
    public static void register(User Object) throws SQLException
    {
        //Extracting the values from the User object
        String username = Object.getUserName();
        String email = Object.getEmail();
        String password = Object.getPassword();

        try (Connection con = getConnection())
        {
            // Use the correct case for table and column names
            String registerQuery = "INSERT INTO User (Username, Email, Password, FullName, UserType) values (?,?,?,?,?)";

            //Creating Prepared Statement
            PreparedStatement registerPS = con.prepareStatement(registerQuery);
            registerPS.setString(1, username);
            registerPS.setString(2, email);
            registerPS.setString(3, password);
            registerPS.setString(4, username); // Use username as FullName for now
            registerPS.setString(5, "user"); // Default user type

            registerPS.executeUpdate();
            System.out.println("Registered Successfully");
        }
        catch(Exception ex)
        {
            System.out.println("Registering Failed: "+ ex.getMessage());
            ex.printStackTrace();
            throw new SQLException("Registration failed", ex);
        }
    }


    //    Function 2: Login
    public static boolean LogIn(String email, String password) throws SQLException
    {
        boolean isLoginSuccessful = false;
        try (Connection con = getConnection())
        {
            // Use the correct case for table and column names
            String loginQuery = "SELECT * FROM User WHERE Email = ?";
            System.out.println("Executing query: " + loginQuery);
            System.out.println("With email: " + email);

            //Prepared Statement
            PreparedStatement loginPS = con.prepareStatement(loginQuery);
            loginPS.setString(1, email);

            //Executing the query
            ResultSet loginCollection = loginPS.executeQuery();

            if (loginCollection.next()) {
                String hashedPassword = loginCollection.getString("Password");
                System.out.println("Stored hashed password: " + hashedPassword);
                System.out.println("Input password: " + password);

                // For testing purposes, allow direct password comparison
                if (password.equals(hashedPassword)) {
                    System.out.println("Direct password match");
                    return true;
                }

                // Also try with password hashing
                isLoginSuccessful = PasswordHash.check(password, hashedPassword);
                System.out.println("Password verification result: " + isLoginSuccessful);
            } else {
                System.out.println("User not found with email: " + email);
            }
            return isLoginSuccessful;
        }
        catch(Exception ex)
        {
            System.out.println("Login Failed: "+ ex.getMessage());
            ex.printStackTrace();
        }
        return isLoginSuccessful;
    }

    /**
     * Validates user credentials and returns the User object if valid
     *
     * @param email User's email
     * @param password User's password
     * @return User object if credentials are valid, null otherwise
     * @throws SQLException if a database error occurs
     */
    public static User validateUser(String email, String password) throws SQLException {
        try (Connection con = getConnection()) {
            // Use the correct case for table and column names
            String query = "SELECT * FROM User WHERE Email = ?";

            try (PreparedStatement ps = con.prepareStatement(query)) {
                ps.setString(1, email);

                try (ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        String hashedPassword = rs.getString("Password");

                        // Check if password matches
                        boolean passwordMatches = password.equals(hashedPassword) || PasswordHash.check(password, hashedPassword);

                        if (passwordMatches) {
                            // Password is correct, create and return the User object
                            String userType = rs.getString("UserType");
                            if (userType == null) userType = "user";

                            User user = new User(
                                rs.getInt("UserId"),
                                0, // RecordId
                                0, // BookId
                                0, // RentalPeriod
                                0, // PlaylistId
                                "", // Title
                                "", // Author
                                "", // Genre
                                "", // Description
                                rs.getString("Username"),
                                rs.getString("Email"),
                                rs.getString("Password"),
                                userType, // UserType
                                null, // RentalDate
                                null, // ReturnDate
                                null, // DateAdded
                                true // Availability
                            );

                            // Try to get profile picture if it exists
                            try {
                                String profilePicture = rs.getString("profile_picture");
                                if (profilePicture != null) {
                                    user.setProfilePicture(profilePicture);
                                }
                            } catch (SQLException e) {
                                // Profile picture column might not exist, ignore
                                System.out.println("Profile picture column not found: " + e.getMessage());
                            }

                            // Try to get full name if it exists
                            try {
                                String fullName = rs.getString("FullName");
                                if (fullName != null) {
                                    user.setFullName(fullName);
                                }
                            } catch (SQLException e) {
                                // FullName column might not exist, ignore
                                System.out.println("FullName column not found: " + e.getMessage());
                            }

                            return user;
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("User validation failed: " + e.getMessage());
            e.printStackTrace();
            throw new SQLException("User validation failed", e);
        }

        return null; // Authentication failed
    }

//    Function 4: Checking for Email duplication
    public static boolean checkEmail(String email) throws SQLException
    {
        boolean isEmailAvailable = false;
        try (Connection con = getConnection())
        {
            // Use the correct case for table and column names
            String checkEmailQuery = "SELECT Email FROM User WHERE Email = ?";
            System.out.println("Executing query: " + checkEmailQuery);
            System.out.println("With email: " + email);

            //Prepared Statement
            PreparedStatement checkEmailPS = con.prepareStatement(checkEmailQuery);
            checkEmailPS.setString(1, email);

            //Executing the query
            ResultSet emailCollection = checkEmailPS.executeQuery();

            isEmailAvailable = emailCollection.next();
            System.out.println("Email exists: " + isEmailAvailable);
            return isEmailAvailable;
        }
        catch(Exception ex)
        {
            System.out.println("Checking Email Failed: "+ ex.getMessage());
            ex.printStackTrace();
        }
        return isEmailAvailable;
    }

    //    Function 3: Get user by email
    public static User getUserByEmail(String email) throws SQLException {
        try (Connection con = getConnection()) {
            // Use the correct case for table and column names
            String query = "SELECT * FROM User WHERE Email = ?";
            System.out.println("Executing query: " + query);
            System.out.println("With email: " + email);

            PreparedStatement ps = con.prepareStatement(query);
            ps.setString(1, email);

            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                System.out.println("User found with email: " + email);
                String userType = rs.getString("UserType");
                if (userType == null) userType = "user";

                User user = new User(
                    rs.getInt("UserId"),
                    0, // RecordId
                    0, // BookId
                    0, // RentalPeriod
                    0, // PlaylistId
                    "", // Title
                    "", // Author
                    "", // Genre
                    "", // Description
                    rs.getString("Username"),
                    rs.getString("Email"),
                    rs.getString("Password"),
                    userType, // UserType
                    null, // RentalDate
                    null, // ReturnDate
                    null, // DateAdded
                    true // Availability
                );

                // Try to get profile picture if it exists
                try {
                    String profilePicture = rs.getString("profile_picture");
                    if (profilePicture != null) {
                        user.setProfilePicture(profilePicture);
                    }
                } catch (SQLException e) {
                    // Profile picture column might not exist, ignore
                    System.out.println("Profile picture column not found: " + e.getMessage());
                }
                return user;
            }
            return null;
        }
    }

    public static boolean verifyPassword(int userId, String password) throws SQLException {
        String query = "SELECT password FROM user WHERE UserId = ?";
        try (Connection conn = getConnection();
             PreparedStatement pstmt = conn.prepareStatement(query)) {
            pstmt.setInt(1, userId);
            ResultSet rs = pstmt.executeQuery();

            if (rs.next()) {
                String storedPassword = rs.getString("password");
                return PasswordHash.check(password, storedPassword);
            }
            return false;
        }
    }

    public static boolean updateUserProfile(User user) throws SQLException {
        String query = "UPDATE user SET Username = ?, email = ?";
        if (user.getPassword() != null && !user.getPassword().isEmpty()) {
            query += ", password = ?";
        }
        if (user.getProfilePicture() != null && !user.getProfilePicture().isEmpty()) {
            query += ", profile_picture = ?";
        }
        query += " WHERE UserId = ?";

        System.out.println("Executing update query: " + query);
        System.out.println("Parameters: Username=" + user.getUserName() + ", email=" + user.getEmail() + ", UserId=" + user.getUserId());

        try (Connection conn = getConnection();
             PreparedStatement pstmt = conn.prepareStatement(query)) {
            int paramIndex = 1;
            pstmt.setString(paramIndex++, user.getUserName());
            pstmt.setString(paramIndex++, user.getEmail());

            if (user.getPassword() != null && !user.getPassword().isEmpty()) {
                pstmt.setString(paramIndex++, user.getPassword());
            }

            if (user.getProfilePicture() != null && !user.getProfilePicture().isEmpty()) {
                pstmt.setString(paramIndex++, user.getProfilePicture());
            }

            pstmt.setInt(paramIndex, user.getUserId());

            System.out.println("Executing update statement...");
            int rowsAffected = pstmt.executeUpdate();
            System.out.println("Rows affected: " + rowsAffected);

            return rowsAffected > 0;
        } catch (SQLException e) {
            System.out.println("Error updating user: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    public static boolean updateProfilePicture(int userId, String profilePicture) throws SQLException {
        String query = "UPDATE user SET profile_picture = ? WHERE UserId = ?";

        System.out.println("Updating profile picture for user ID: " + userId);
        System.out.println("Profile picture length: " + (profilePicture != null ? profilePicture.length() : 0));

        try (Connection conn = getConnection();
             PreparedStatement pstmt = conn.prepareStatement(query)) {
            pstmt.setString(1, profilePicture);
            pstmt.setInt(2, userId);

            int rowsAffected = pstmt.executeUpdate();
            System.out.println("Rows affected by profile picture update: " + rowsAffected);

            // Verify the update
            String verifyQuery = "SELECT profile_picture FROM user WHERE UserId = ?";
            try (PreparedStatement verifyStmt = conn.prepareStatement(verifyQuery)) {
                verifyStmt.setInt(1, userId);
                ResultSet rs = verifyStmt.executeQuery();
                if (rs.next()) {
                    String storedPicture = rs.getString("profile_picture");
                    System.out.println("Stored profile picture length: " + (storedPicture != null ? storedPicture.length() : 0));
                }
            }

            return rowsAffected > 0;
        }
    }

    // Get total number of users
    public static int getTotalUsers() throws SQLException {
        try (Connection con = getConnection()) {
            String query = "SELECT COUNT(*) FROM User";

            try (PreparedStatement ps = con.prepareStatement(query)) {
                ResultSet rs = ps.executeQuery();

                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting total users: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }

        return 0;
    }

    // Get recent users
    public static List<User> getRecentUsers(int limit) throws SQLException {
        List<User> users = new ArrayList<>();

        try (Connection con = getConnection()) {
            String query = "SELECT * FROM User ORDER BY created_at DESC LIMIT ?";

            try (PreparedStatement ps = con.prepareStatement(query)) {
                ps.setInt(1, limit);
                ResultSet rs = ps.executeQuery();

                while (rs.next()) {
                    User user = new User();
                    user.setUserId(rs.getInt("UserId"));
                    user.setUserName(rs.getString("Username"));
                    user.setEmail(rs.getString("Email"));
                    user.setFullName(rs.getString("FullName"));
                    user.setUserType(rs.getString("UserType"));
                    // Add other fields as needed

                    users.add(user);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting recent users: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }

        return users;
    }

}
