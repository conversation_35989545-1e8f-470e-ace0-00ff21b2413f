package Model;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class BookDAO {
    private Connection connection;

    public BookDAO() {
        try {
            System.out.println("Initializing BookDAO...");
            connection = DatabaseConnection.getConnection();
            if (connection == null) {
                System.err.println("Failed to get database connection");
                throw new SQLException("Database connection is null");
            } else {
                System.out.println("Database connection established successfully");
                // Test the connection
                if (connection.isClosed()) {
                    System.err.println("Database connection is closed");
                    throw new SQLException("Database connection is closed");
                }
            }
        } catch (SQLException e) {
            System.err.println("Error initializing BookDAO: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to initialize BookDAO", e);
        }
    }

    public Book getBookById(int bookId) {
        System.out.println("Getting book by ID: " + bookId);
        Book book = null;
        String query = "SELECT * FROM book WHERE book_id = ?";

        try {
            if (connection == null || connection.isClosed()) {
                System.err.println("Database connection is null or closed");
                throw new SQLException("Database connection is not available");
            }

            System.out.println("Preparing statement: " + query);
            try (PreparedStatement statement = connection.prepareStatement(query)) {
                statement.setInt(1, bookId);
                System.out.println("Executing query with bookId: " + bookId);

                ResultSet resultSet = statement.executeQuery();
                System.out.println("Query executed successfully");

                if (resultSet.next()) {
                    book = new Book();
                    book.setBookId(resultSet.getInt("book_id"));
                    book.setTitle(resultSet.getString("title"));
                    book.setAuthor(resultSet.getString("author"));
                    book.setGenre(resultSet.getString("genre"));
                    book.setDescription(resultSet.getString("description"));
                    book.setIsbn(resultSet.getString("isbn"));
                    book.setImageUrl(resultSet.getString("image_url"));
                    book.setAvailable(resultSet.getBoolean("available"));
                    book.setCreatedAt(resultSet.getTimestamp("created_at"));
                    book.setUpdatedAt(resultSet.getTimestamp("updated_at"));
                    System.out.println("Book found: " + book.getTitle());
                } else {
                    System.out.println("No book found with ID: " + bookId);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting book by ID: " + e.getMessage());
            System.err.println("SQL State: " + e.getSQLState());
            System.err.println("Error Code: " + e.getErrorCode());
            e.printStackTrace();
            throw new RuntimeException("Failed to get book by ID", e);
        }

        return book;
    }

    public List<Book> getAllBooks() {
        System.out.println("Getting all books");
        List<Book> books = new ArrayList<>();
        String query = "SELECT * FROM book";

        try {
            if (connection == null || connection.isClosed()) {
                System.err.println("Database connection is null or closed");
                throw new SQLException("Database connection is not available");
            }

            try (PreparedStatement statement = connection.prepareStatement(query)) {
                ResultSet resultSet = statement.executeQuery();

                while (resultSet.next()) {
                    Book book = new Book();
                    book.setBookId(resultSet.getInt("book_id"));
                    book.setTitle(resultSet.getString("title"));
                    book.setAuthor(resultSet.getString("author"));
                    book.setGenre(resultSet.getString("genre"));
                    book.setDescription(resultSet.getString("description"));
                    book.setIsbn(resultSet.getString("isbn"));
                    book.setImageUrl(resultSet.getString("image_url"));
                    book.setAvailable(resultSet.getBoolean("available"));
                    book.setCreatedAt(resultSet.getTimestamp("created_at"));
                    book.setUpdatedAt(resultSet.getTimestamp("updated_at"));
                    books.add(book);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting all books: " + e.getMessage());
            System.err.println("SQL State: " + e.getSQLState());
            System.err.println("Error Code: " + e.getErrorCode());
            e.printStackTrace();
            throw new RuntimeException("Failed to get all books", e);
        }

        return books;
    }

    public boolean updateBookAvailability(int bookId, boolean available) {
        System.out.println("Updating book availability - Book ID: " + bookId + ", Available: " + available);
        String query = "UPDATE book SET available = ?, updated_at = CURRENT_TIMESTAMP WHERE book_id = ?";

        try {
            if (connection == null || connection.isClosed()) {
                System.err.println("Database connection is null or closed");
                throw new SQLException("Database connection is not available");
            }

            try (PreparedStatement statement = connection.prepareStatement(query)) {
                statement.setBoolean(1, available);
                statement.setInt(2, bookId);
                int rowsAffected = statement.executeUpdate();
                System.out.println("Update executed successfully. Rows affected: " + rowsAffected);
                return rowsAffected > 0;
            }
        } catch (SQLException e) {
            System.err.println("Error updating book availability: " + e.getMessage());
            System.err.println("SQL State: " + e.getSQLState());
            System.err.println("Error Code: " + e.getErrorCode());
            e.printStackTrace();
            throw new RuntimeException("Failed to update book availability", e);
        }
    }

    /**
     * Search for books by keyword in title, author, genre, or description
     * @param keyword The search keyword
     * @return List of books matching the search criteria
     */
    public List<Book> searchBooks(String keyword) {
        System.out.println("Searching for books with keyword: " + keyword);
        List<Book> books = new ArrayList<>();
        String query = "SELECT * FROM book WHERE title LIKE ? OR author LIKE ? OR genre LIKE ? OR description LIKE ? ORDER BY title";

        try {
            if (connection == null || connection.isClosed()) {
                System.err.println("Database connection is null or closed");
                throw new SQLException("Database connection is not available");
            }

            try (PreparedStatement statement = connection.prepareStatement(query)) {
                String searchPattern = "%" + keyword + "%";
                statement.setString(1, searchPattern);
                statement.setString(2, searchPattern);
                statement.setString(3, searchPattern);
                statement.setString(4, searchPattern);

                System.out.println("Executing search query: " + query.replace("?", "'" + searchPattern + "'"));
                ResultSet resultSet = statement.executeQuery();

                while (resultSet.next()) {
                    Book book = new Book();
                    book.setBookId(resultSet.getInt("book_id"));
                    book.setTitle(resultSet.getString("title"));
                    book.setAuthor(resultSet.getString("author"));
                    book.setGenre(resultSet.getString("genre"));
                    book.setDescription(resultSet.getString("description"));
                    book.setIsbn(resultSet.getString("isbn"));
                    book.setImageUrl(resultSet.getString("image_url"));
                    book.setAvailable(resultSet.getBoolean("available"));
                    book.setCreatedAt(resultSet.getTimestamp("created_at"));
                    book.setUpdatedAt(resultSet.getTimestamp("updated_at"));
                    books.add(book);
                }

                System.out.println("Found " + books.size() + " books matching the search criteria");
            }
        } catch (SQLException e) {
            System.err.println("Error searching books: " + e.getMessage());
            System.err.println("SQL State: " + e.getSQLState());
            System.err.println("Error Code: " + e.getErrorCode());
            e.printStackTrace();
            throw new RuntimeException("Failed to search books", e);
        }

        return books;
    }
}