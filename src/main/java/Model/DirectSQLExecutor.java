package Model;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * Utility class for executing direct SQL statements.
 * This is useful for operations that require more control than the standard DAO methods provide.
 */
public class DirectSQLExecutor {

    /**
     * Executes a direct SQL statement that doesn't return a result set (INSERT, UPDATE, DELETE).
     *
     * @param sql The SQL statement to execute
     * @return The number of rows affected
     * @throws SQLException If a database access error occurs
     */
    public static int executeUpdate(String sql) throws SQLException {
        try (Connection conn = DatabaseConnection.getConnection();
             Statement stmt = conn.createStatement()) {
            return stmt.executeUpdate(sql);
        }
    }

    /**
     * Executes a direct SQL statement with parameters that doesn't return a result set.
     *
     * @param sql The SQL statement to execute
     * @param params The parameters to set in the prepared statement
     * @return The number of rows affected
     * @throws SQLException If a database access error occurs
     */
    public static int executeUpdate(String sql, Object... params) throws SQLException {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {

            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }

            return ps.executeUpdate();
        }
    }

    /**
     * Executes a direct SQL query that returns a result set.
     * The caller is responsible for closing the ResultSet and Statement.
     *
     * @param sql The SQL query to execute
     * @return The ResultSet containing the query results
     * @throws SQLException If a database access error occurs
     */
    public static ResultSet executeQuery(String sql) throws SQLException {
        Connection conn = DatabaseConnection.getConnection();
        Statement stmt = conn.createStatement();
        return stmt.executeQuery(sql);
    }

    /**
     * Executes a direct SQL query with parameters that returns a result set.
     * The caller is responsible for closing the ResultSet and PreparedStatement.
     *
     * @param sql The SQL query to execute
     * @param params The parameters to set in the prepared statement
     * @return The ResultSet containing the query results
     * @throws SQLException If a database access error occurs
     */
    public static ResultSet executeQuery(String sql, Object... params) throws SQLException {
        Connection conn = DatabaseConnection.getConnection();
        PreparedStatement ps = conn.prepareStatement(sql);

        for (int i = 0; i < params.length; i++) {
            ps.setObject(i + 1, params[i]);
        }

        return ps.executeQuery();
    }

    /**
     * Executes a direct SQL statement within a transaction.
     *
     * @param sql The SQL statement to execute
     * @return The number of rows affected
     * @throws SQLException If a database access error occurs
     */
    public static int executeUpdateWithTransaction(String sql) throws SQLException {
        Connection conn = null;
        try {
            conn = DatabaseConnection.getConnection();
            conn.setAutoCommit(false);

            try (Statement stmt = conn.createStatement()) {
                int result = stmt.executeUpdate(sql);
                conn.commit();
                return result;
            }
        } catch (SQLException e) {
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    ex.printStackTrace();
                }
            }
            throw e;
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    conn.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * Executes a direct SQL statement with parameters within a transaction.
     *
     * @param sql The SQL statement to execute
     * @param params The parameters to set in the prepared statement
     * @return The number of rows affected
     * @throws SQLException If a database access error occurs
     */
    public static int executeUpdateWithTransaction(String sql, Object... params) throws SQLException {
        Connection conn = null;
        try {
            conn = DatabaseConnection.getConnection();
            conn.setAutoCommit(false);

            try (PreparedStatement ps = conn.prepareStatement(sql)) {
                for (int i = 0; i < params.length; i++) {
                    ps.setObject(i + 1, params[i]);
                }

                int result = ps.executeUpdate();
                conn.commit();
                return result;
            }
        } catch (SQLException e) {
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    ex.printStackTrace();
                }
            }
            throw e;
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    conn.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * Deletes a user and all related records using multiple approaches to ensure success.
     * This is a last-resort method when other deletion methods fail.
     *
     * @param userId The ID of the user to delete
     * @return true if the user was deleted successfully, false otherwise
     */
    public static boolean forceDeleteUser(int userId) {
        Connection conn = null;
        try {
            conn = DatabaseConnection.getConnection();
            conn.setAutoCommit(false);

            System.out.println("Starting deletion process for user ID: " + userId);

            // First, check if the user exists
            String checkUserQuery = "SELECT COUNT(*) FROM User WHERE UserId = ?";
            try (PreparedStatement ps = conn.prepareStatement(checkUserQuery)) {
                ps.setInt(1, userId);
                ResultSet rs = ps.executeQuery();
                if (rs.next() && rs.getInt(1) == 0) {
                    System.out.println("User with ID " + userId + " does not exist");
                    return false;
                }
            }

            // Try to delete from specific tables first
            try {
                // Delete from Rental_Details first
                String deleteRentalDetailsQuery = "DELETE FROM Rental_Details WHERE user_id = ?";
                try (PreparedStatement ps = conn.prepareStatement(deleteRentalDetailsQuery)) {
                    ps.setInt(1, userId);
                    int count = ps.executeUpdate();
                    System.out.println("Deleted " + count + " records from Rental_Details");
                }
            } catch (SQLException e) {
                System.out.println("Error deleting from Rental_Details: " + e.getMessage());
                // Continue with other tables
            }

            try {
                // Delete from Rental_Record
                String deleteRentalRecordQuery = "DELETE FROM Rental_Record WHERE user_id = ?";
                try (PreparedStatement ps = conn.prepareStatement(deleteRentalRecordQuery)) {
                    ps.setInt(1, userId);
                    int count = ps.executeUpdate();
                    System.out.println("Deleted " + count + " records from Rental_Record");
                }
            } catch (SQLException e) {
                System.out.println("Error deleting from Rental_Record: " + e.getMessage());
                // Continue with other tables
            }

            // Finally, delete the user
            String deleteUserQuery = "DELETE FROM User WHERE UserId = ?";
            try (PreparedStatement ps = conn.prepareStatement(deleteUserQuery)) {
                ps.setInt(1, userId);
                int count = ps.executeUpdate();
                System.out.println("Deleted user with ID: " + userId + ", affected rows: " + count);

                if (count > 0) {
                    conn.commit();
                    return true;
                }
            } catch (SQLException e) {
                System.out.println("Error with standard delete: " + e.getMessage());
                // Try alternative approach
            }

            // If we get here, try a more direct approach
            try {
                // Use a direct SQL statement (case-sensitive)
                String directSql = "DELETE FROM User WHERE UserId = " + userId;
                try (Statement stmt = conn.createStatement()) {
                    int count = stmt.executeUpdate(directSql);
                    System.out.println("Direct SQL delete affected rows: " + count);

                    if (count > 0) {
                        conn.commit();
                        return true;
                    }
                }
            } catch (SQLException e) {
                System.out.println("Error with direct SQL: " + e.getMessage());
            }

            // If we get here, try one more approach with a different case for the table name
            try {
                String alternateSql = "DELETE FROM user WHERE UserId = " + userId;
                try (Statement stmt = conn.createStatement()) {
                    int count = stmt.executeUpdate(alternateSql);
                    System.out.println("Alternate SQL delete affected rows: " + count);

                    if (count > 0) {
                        conn.commit();
                        return true;
                    }
                }
            } catch (SQLException e) {
                System.out.println("Error with alternate SQL: " + e.getMessage());
            }

            // Try a brute force approach as a last resort
            try {
                System.out.println("Trying brute force approach for user ID: " + userId);
                boolean bruteForceSuccess = bruteForceDeleteUser(userId);
                if (bruteForceSuccess) {
                    conn.commit();
                    return true;
                }
            } catch (Exception e) {
                System.out.println("Error with brute force approach: " + e.getMessage());
            }

            // If we get here, all approaches failed
            conn.rollback();
            return false;
        } catch (SQLException e) {
            System.err.println("Error deleting user: " + e.getMessage());
            e.printStackTrace();

            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    System.err.println("Error rolling back transaction: " + ex.getMessage());
                }
            }

            return false;
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    conn.close();
                } catch (SQLException e) {
                    System.err.println("Error closing connection: " + e.getMessage());
                }
            }
        }
    }

    /**
     * Gets all table names in the database.
     *
     * @return A list of table names
     */
    public static List<String> getAllTableNames() {
        return getAllTableNames(false);
    }

    /**
     * Gets all table names in the database.
     *
     * @param includeSystemTables Whether to include system tables
     * @return A list of table names
     */
    public static List<String> getAllTableNames(boolean includeSystemTables) {
        List<String> tableNames = new ArrayList<>();
        Connection conn = null;

        try {
            conn = DatabaseConnection.getConnection();

            // Get all tables in the database
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery("SHOW TABLES")) {

                while (rs.next()) {
                    String tableName = rs.getString(1);
                    tableNames.add(tableName);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting table names: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    System.err.println("Error closing connection: " + e.getMessage());
                }
            }
        }

        return tableNames;
    }

    /**
     * Gets all columns in a table.
     *
     * @param tableName The name of the table
     * @return A list of column names
     */
    public static List<String> getTableColumns(String tableName) {
        List<String> columnNames = new ArrayList<>();
        Connection conn = null;

        try {
            conn = DatabaseConnection.getConnection();

            // Get all columns in the table
            String query = "SHOW COLUMNS FROM " + tableName;
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(query)) {

                while (rs.next()) {
                    String columnName = rs.getString("Field");
                    String columnType = rs.getString("Type");
                    String isNullable = rs.getString("Null");
                    String key = rs.getString("Key");
                    String defaultValue = rs.getString("Default");
                    String extra = rs.getString("Extra");

                    // Format: Field (Type, Nullable, Key, Default, Extra)
                    StringBuilder columnInfo = new StringBuilder(columnName);
                    columnInfo.append(" (")
                              .append(columnType);

                    if ("NO".equals(isNullable)) {
                        columnInfo.append(", NOT NULL");
                    }

                    if (key != null && !key.isEmpty()) {
                        columnInfo.append(", ").append(key);
                    }

                    if (defaultValue != null) {
                        columnInfo.append(", DEFAULT ").append(defaultValue);
                    }

                    if (extra != null && !extra.isEmpty()) {
                        columnInfo.append(", ").append(extra);
                    }

                    columnInfo.append(")");
                    columnNames.add(columnInfo.toString());
                }
            }
        } catch (SQLException e) {
            System.err.println("Error getting columns for table " + tableName + ": " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    System.err.println("Error closing connection: " + e.getMessage());
                }
            }
        }

        return columnNames;
    }

    /**
     * Brute force approach to delete a user by trying multiple SQL variations and table names.
     * This is a last-resort method when all other deletion methods fail.
     *
     * @param userId The ID of the user to delete
     * @return true if the user was deleted successfully, false otherwise
     */
    private static boolean bruteForceDeleteUser(int userId) {
        Connection conn = null;
        boolean success = false;

        try {
            conn = DatabaseConnection.getConnection();
            conn.setAutoCommit(false);

            System.out.println("Starting brute force deletion for user ID: " + userId);

            // Try to delete from all tables that might have a user_id column
            String[] possibleTables = {
                "Rental_Details", "rental_details", "rentaldetails", "RentalDetails",
                "Rental_Record", "rental_record", "rentalrecord", "RentalRecord",
                "User_Activity", "user_activity", "useractivity", "UserActivity"
            };

            for (String table : possibleTables) {
                try {
                    String[] possibleColumns = {"user_id", "userId", "UserId", "userid"};

                    for (String column : possibleColumns) {
                        try {
                            String sql = "DELETE FROM " + table + " WHERE " + column + " = ?";
                            try (PreparedStatement ps = conn.prepareStatement(sql)) {
                                ps.setInt(1, userId);
                                int count = ps.executeUpdate();
                                System.out.println("Deleted " + count + " records from " + table + " using column " + column);
                            }
                        } catch (SQLException e) {
                            // Ignore errors and continue with next column
                        }
                    }
                } catch (Exception e) {
                    // Ignore errors and continue with next table
                }
            }

            // Try to delete the user with multiple table name variations
            String[] possibleUserTables = {"User", "user", "users", "Users", "USER"};
            String[] possibleIdColumns = {"UserId", "userId", "userid", "user_id", "id", "ID"};

            for (String table : possibleUserTables) {
                for (String column : possibleIdColumns) {
                    try {
                        // Try with prepared statement
                        String sql = "DELETE FROM " + table + " WHERE " + column + " = ?";
                        try (PreparedStatement ps = conn.prepareStatement(sql)) {
                            ps.setInt(1, userId);
                            int count = ps.executeUpdate();
                            System.out.println("Deleted user from " + table + " using column " + column + ", affected rows: " + count);

                            if (count > 0) {
                                success = true;
                            }
                        }
                    } catch (SQLException e) {
                        // Ignore errors and continue with next combination
                    }

                    try {
                        // Try with direct SQL
                        String sql = "DELETE FROM " + table + " WHERE " + column + " = " + userId;
                        try (Statement stmt = conn.createStatement()) {
                            int count = stmt.executeUpdate(sql);
                            System.out.println("Direct SQL: Deleted user from " + table + " using column " + column + ", affected rows: " + count);

                            if (count > 0) {
                                success = true;
                            }
                        }
                    } catch (SQLException e) {
                        // Ignore errors and continue with next combination
                    }
                }
            }

            if (success) {
                conn.commit();
                System.out.println("Brute force deletion successful for user ID: " + userId);
            } else {
                conn.rollback();
                System.out.println("Brute force deletion failed for user ID: " + userId);
            }

            return success;
        } catch (Exception e) {
            System.err.println("Error in brute force deletion: " + e.getMessage());
            e.printStackTrace();

            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    System.err.println("Error rolling back transaction: " + ex.getMessage());
                }
            }

            return false;
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    conn.close();
                } catch (SQLException e) {
                    System.err.println("Error closing connection: " + e.getMessage());
                }
            }
        }
    }
}
