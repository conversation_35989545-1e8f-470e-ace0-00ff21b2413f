-- Create tables for student management system
USE advancejavacoursework;

-- Extend User table to include student-specific fields
ALTER TABLE User ADD COLUMN student_id VARCHAR(20) NULL;
ALTER TABLE User ADD COLUMN enrollment_date DATE NULL;
ALTER TABLE User ADD COLUMN graduation_date DATE NULL;
ALTER TABLE User ADD COLUMN department VARCHAR(100) NULL;
ALTER TABLE User ADD COLUMN program VARCHAR(100) NULL;
ALTER TABLE User ADD COLUMN semester INT NULL;
ALTER TABLE User MODIFY COLUMN UserType ENUM('admin', 'user', 'student', 'teacher') NOT NULL DEFAULT 'user';

-- Create Course table
CREATE TABLE IF NOT EXISTS Course (
    course_id INT AUTO_INCREMENT PRIMARY KEY,
    course_code VARCHAR(20) NOT NULL UNIQUE,
    course_name VARCHAR(100) NOT NULL,
    description TEXT,
    credits INT NOT NULL,
    department VARCHAR(100),
    teacher_id INT,
    semester INT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (teacher_id) REFERENCES User(UserId) ON DELETE SET NULL
);

-- Create Enrollment table (students enrolled in courses)
CREATE TABLE IF NOT EXISTS Enrollment (
    enrollment_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    course_id INT NOT NULL,
    enrollment_date DATE NOT NULL,
    status ENUM('active', 'completed', 'dropped', 'pending') DEFAULT 'active',
    grade VARCHAR(5) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES User(UserId) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES Course(course_id) ON DELETE CASCADE,
    UNIQUE KEY (student_id, course_id)
);

-- Create Attendance table
CREATE TABLE IF NOT EXISTS Attendance (
    attendance_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    course_id INT NOT NULL,
    attendance_date DATE NOT NULL,
    status ENUM('present', 'absent', 'late', 'excused') NOT NULL DEFAULT 'present',
    remarks TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES User(UserId) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES Course(course_id) ON DELETE CASCADE,
    UNIQUE KEY (student_id, course_id, attendance_date)
);

-- Create Assignment table
CREATE TABLE IF NOT EXISTS Assignment (
    assignment_id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    due_date DATE,
    total_marks INT NOT NULL,
    weight DECIMAL(5,2) DEFAULT 1.00,
    file_path VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES Course(course_id) ON DELETE CASCADE
);

-- Create Assignment Submission table
CREATE TABLE IF NOT EXISTS Assignment_Submission (
    submission_id INT AUTO_INCREMENT PRIMARY KEY,
    assignment_id INT NOT NULL,
    student_id INT NOT NULL,
    submission_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    file_path VARCHAR(255),
    marks_obtained DECIMAL(5,2) DEFAULT NULL,
    feedback TEXT,
    status ENUM('submitted', 'graded', 'late', 'resubmit') DEFAULT 'submitted',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (assignment_id) REFERENCES Assignment(assignment_id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES User(UserId) ON DELETE CASCADE,
    UNIQUE KEY (assignment_id, student_id)
);

-- Create Exam table
CREATE TABLE IF NOT EXISTS Exam (
    exam_id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    exam_name VARCHAR(100) NOT NULL,
    exam_date DATE NOT NULL,
    duration INT NOT NULL, -- in minutes
    total_marks INT NOT NULL,
    weight DECIMAL(5,2) DEFAULT 1.00,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES Course(course_id) ON DELETE CASCADE
);

-- Create Exam Result table
CREATE TABLE IF NOT EXISTS Exam_Result (
    result_id INT AUTO_INCREMENT PRIMARY KEY,
    exam_id INT NOT NULL,
    student_id INT NOT NULL,
    marks_obtained DECIMAL(5,2) NOT NULL,
    grade VARCHAR(5),
    remarks TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (exam_id) REFERENCES Exam(exam_id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES User(UserId) ON DELETE CASCADE,
    UNIQUE KEY (exam_id, student_id)
);

-- Create Course Material table
CREATE TABLE IF NOT EXISTS Course_Material (
    material_id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    material_type ENUM('lecture_note', 'presentation', 'video', 'document', 'link', 'other') NOT NULL,
    file_path VARCHAR(255),
    external_link VARCHAR(255),
    upload_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES Course(course_id) ON DELETE CASCADE
);

-- Create Announcement table
CREATE TABLE IF NOT EXISTS Announcement (
    announcement_id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT,
    user_id INT NOT NULL,
    title VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    announcement_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES Course(course_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES User(UserId) ON DELETE CASCADE
);

-- Insert sample data for testing
-- Sample courses
INSERT INTO Course (course_code, course_name, description, credits, department, semester, is_active)
VALUES 
('CS101', 'Introduction to Computer Science', 'Fundamental concepts of computer science including algorithms, data structures, and problem-solving techniques.', 3, 'Computer Science', 1, TRUE),
('CS201', 'Data Structures and Algorithms', 'Advanced data structures and algorithm design and analysis.', 4, 'Computer Science', 2, TRUE),
('MATH101', 'Calculus I', 'Introduction to differential and integral calculus.', 3, 'Mathematics', 1, TRUE),
('ENG101', 'English Composition', 'Fundamentals of academic writing and critical thinking.', 3, 'English', 1, TRUE),
('PHYS101', 'Physics I', 'Introduction to mechanics and thermodynamics.', 4, 'Physics', 1, TRUE);

-- Update some users to be students
UPDATE User SET UserType = 'student', student_id = 'STU001', enrollment_date = '2023-09-01', department = 'Computer Science', program = 'Bachelor of Computer Science', semester = 1 WHERE Username = 'user1';

-- Insert a teacher
INSERT INTO User (Username, Password, Email, FullName, UserType, department) 
VALUES ('teacher1', 'teacher123', '<EMAIL>', 'John Smith', 'teacher', 'Computer Science')
ON DUPLICATE KEY UPDATE Username = Username;

-- Update courses with teacher
UPDATE Course SET teacher_id = (SELECT UserId FROM User WHERE Username = 'teacher1' LIMIT 1);

-- Enroll students in courses
INSERT INTO Enrollment (student_id, course_id, enrollment_date, status)
SELECT 
    (SELECT UserId FROM User WHERE Username = 'user1' LIMIT 1),
    course_id,
    '2023-09-01',
    'active'
FROM Course
WHERE course_code IN ('CS101', 'MATH101', 'ENG101');

-- Add some attendance records
INSERT INTO Attendance (student_id, course_id, attendance_date, status)
VALUES 
((SELECT UserId FROM User WHERE Username = 'user1' LIMIT 1), 
 (SELECT course_id FROM Course WHERE course_code = 'CS101' LIMIT 1),
 '2023-09-05', 'present'),
((SELECT UserId FROM User WHERE Username = 'user1' LIMIT 1), 
 (SELECT course_id FROM Course WHERE course_code = 'CS101' LIMIT 1),
 '2023-09-07', 'present'),
((SELECT UserId FROM User WHERE Username = 'user1' LIMIT 1), 
 (SELECT course_id FROM Course WHERE course_code = 'MATH101' LIMIT 1),
 '2023-09-06', 'present');

-- Add some assignments
INSERT INTO Assignment (course_id, title, description, due_date, total_marks)
VALUES 
((SELECT course_id FROM Course WHERE course_code = 'CS101' LIMIT 1),
 'Introduction to Programming', 'Write a simple program to calculate factorial of a number.', 
 '2023-09-30', 100),
((SELECT course_id FROM Course WHERE course_code = 'MATH101' LIMIT 1),
 'Calculus Problem Set 1', 'Solve the given problems on limits and derivatives.', 
 '2023-09-25', 50);

-- Add some course materials
INSERT INTO Course_Material (course_id, title, description, material_type, file_path)
VALUES 
((SELECT course_id FROM Course WHERE course_code = 'CS101' LIMIT 1),
 'Introduction to Programming Concepts', 'Lecture notes on basic programming concepts', 
 'lecture_note', 'assets/course_materials/cs101/lecture1.pdf'),
((SELECT course_id FROM Course WHERE course_code = 'CS101' LIMIT 1),
 'Algorithm Flowcharts', 'Visual representation of algorithms', 
 'document', 'assets/course_materials/cs101/flowcharts.pdf'),
((SELECT course_id FROM Course WHERE course_code = 'MATH101' LIMIT 1),
 'Calculus Fundamentals', 'Introduction to calculus concepts', 
 'lecture_note', 'assets/course_materials/math101/lecture1.pdf');

-- Add some announcements
INSERT INTO Announcement (course_id, user_id, title, content)
VALUES 
((SELECT course_id FROM Course WHERE course_code = 'CS101' LIMIT 1),
 (SELECT UserId FROM User WHERE Username = 'teacher1' LIMIT 1),
 'Welcome to CS101', 'Welcome to Introduction to Computer Science! Please review the syllabus and come prepared for our first class.'),
((SELECT course_id FROM Course WHERE course_code = 'MATH101' LIMIT 1),
 (SELECT UserId FROM User WHERE Username = 'teacher1' LIMIT 1),
 'Calculus Textbook', 'Please make sure to purchase the required textbook before our next class.');
