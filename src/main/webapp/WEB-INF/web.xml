<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="https://jakarta.ee/xml/ns/jakartaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="https://jakarta.ee/xml/ns/jakartaee https://jakarta.ee/xml/ns/jakartaee/web-app_6_0.xsd"
         version="6.0">

    <servlet>
        <servlet-name>ProfileUpdateServlet</servlet-name>
        <servlet-class>Controller.ProfileUpdateServlet</servlet-class>
        <multipart-config>
            <max-file-size>10485760</max-file-size>
            <max-request-size>10485760</max-request-size>
            <file-size-threshold>0</file-size-threshold>
        </multipart-config>
    </servlet>

    <servlet-mapping>
        <servlet-name>ProfileUpdateServlet</servlet-name>
        <url-pattern>/profile/update</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>DatabaseSetupServlet</servlet-name>
        <servlet-class>Controller.DatabaseSetupServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet>
        <servlet-name>AdminReportServlet</servlet-name>
        <servlet-class>Controller.AdminReportServlet</servlet-class>
    </servlet>

    <servlet-mapping>
        <servlet-name>AdminReportServlet</servlet-name>
        <url-pattern>/admin/reports/*</url-pattern>
    </servlet-mapping>

    <welcome-file-list>
        <welcome-file>View/LogIn.jsp</welcome-file>
    </welcome-file-list>

    <!-- Session Configuration -->
    <session-config>
        <session-timeout>30</session-timeout>
    </session-config>

    <!-- Error Pages -->
    <error-page>
        <error-code>404</error-code>
        <location>/View/error404.jsp</location>
    </error-page>

    <error-page>
        <error-code>500</error-code>
        <location>/View/error500.jsp</location>
    </error-page>

    <!-- Filters -->
    <filter>
        <filter-name>AdminAuthFilter</filter-name>
        <filter-class>Filter.AdminAuthFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>AdminAuthFilter</filter-name>
        <url-pattern>/index.jsp</url-pattern>
        <url-pattern>/View/Category.jsp</url-pattern>
        <url-pattern>/View/UserDashboard.jsp</url-pattern>
        <url-pattern>/View/about.jsp</url-pattern>
        <url-pattern>/View/Contact.jsp</url-pattern>
        <url-pattern>/user-rentals</url-pattern>
        <url-pattern>/rent-book</url-pattern>
        <url-pattern>/view-rental/*</url-pattern>
        <url-pattern>/edit-rental</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>UserAuthFilter</filter-name>
        <filter-class>Filter.UserAuthFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>UserAuthFilter</filter-name>
        <url-pattern>/View/AdminDashboard.jsp</url-pattern>
        <url-pattern>/View/adminUsers.jsp</url-pattern>
        <url-pattern>/View/adminBooks.jsp</url-pattern>
        <url-pattern>/View/adminRentals.jsp</url-pattern>
        <url-pattern>/View/adminAddBook.jsp</url-pattern>
        <url-pattern>/View/adminEditBook.jsp</url-pattern>
        <url-pattern>/View/adminEditUser.jsp</url-pattern>
        <url-pattern>/admin/*</url-pattern>
    </filter-mapping>
</web-app>