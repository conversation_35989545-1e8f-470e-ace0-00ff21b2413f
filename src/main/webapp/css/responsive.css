/* Responsive Styles */
:root {
    --primary: #5b6dfa;
    --secondary: #34c9c9;
    --accent: #b388ff;
    --background: #f7faff;
    --white: #fff;
    --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
    --radius: 12px;
    --input-bg: #f0f4ff;
    --input-border: #e0e7ff;
    --text-main: #222;
    --text-light: #666;
}

/* Common Responsive Styles */
@media (max-width: 1200px) {
    .dashboard-container,
    .footer-content {
        max-width: 95%;
        padding: 0 20px;
    }
}

@media (max-width: 992px) {
    /* Navbar Responsive */
    .nav-top {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .search-section {
        width: 100%;
        flex-direction: column;
        gap: 1rem;
    }

    .search-container {
        width: 100%;
    }

    .search-input {
        width: 100%;
    }

    .auth-buttons {
        width: 100%;
        justify-content: center;
    }

    /* Dashboard Responsive */
    .dashboard-container {
        grid-template-columns: 1fr;
    }

    .sidebar {
        position: static;
        margin-bottom: 2rem;
    }

    /* Footer Responsive */
    .footer-content {
        flex-direction: column;
        gap: 2rem;
    }

    .footer-right {
        flex-wrap: wrap;
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    /* Navbar Responsive */
    .nav-bottom {
        display: none;
    }

    .nav-links {
        flex-direction: column;
        gap: 1rem;
    }

    /* Login/Register Card Responsive */
    .login-card,
    .register-card {
        padding: 1rem;
    }

    .login-title {
        font-size: 1.2rem;
    }

    .login-desc {
        font-size: 0.9rem;
    }

    /* Profile Section Responsive */
    .profile-pic {
        width: 100px;
        height: 100px;
        font-size: 2rem;
    }

    .profile-name {
        font-size: 1.2rem;
    }

    .profile-email {
        font-size: 0.9rem;
    }

    /* Form Elements Responsive */
    .form-group input {
        padding: 12px;
    }

    .btn {
        padding: 12px 24px;
    }
}

@media (max-width: 576px) {
    /* Navbar Responsive */
    .logo-section {
        flex-direction: column;
        text-align: center;
    }

    .website-name {
        font-size: 1.2rem;
    }

    /* Login/Register Card Responsive */
    .login-card,
    .register-card {
        margin: 1rem;
    }

    /* Profile Section Responsive */
    .profile-pic {
        width: 80px;
        height: 80px;
        font-size: 1.5rem;
    }

    /* Books Grid Responsive */
    .books-grid {
        grid-template-columns: 1fr;
    }

    .book-card {
        max-width: 100%;
    }
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
        background: none;
        border: none;
        color: var(--primary);
        font-size: 1.5rem;
        cursor: pointer;
        padding: 1rem;
    }

    .nav-bottom {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        background: var(--white);
        padding: 1rem;
        box-shadow: var(--shadow);
        z-index: 1000;
    }

    .nav-bottom.active {
        display: block;
    }

    .nav-links {
        flex-direction: column;
        gap: 1rem;
    }
}

/* Responsive Tables */
.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

@media (max-width: 768px) {
    table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }

    th, td {
        padding: 8px 10px;
        font-size: 0.9rem;
    }
}

/* Responsive Images */
img {
    max-width: 100%;
    height: auto;
}

/* Responsive Typography */
@media (max-width: 768px) {
    h1 { font-size: 1.8rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.2rem; }
    p { font-size: 0.9rem; }
}

/* Responsive Forms */
@media (max-width: 768px) {
    .form-group {
        margin-bottom: 0.75rem;
    }

    input, select, textarea {
        font-size: 0.9rem;
        padding: 8px 12px;
    }

    .btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .form-group {
        margin-bottom: 0.5rem;
    }

    .btn {
        width: 100%;
        justify-content: center;
        margin-bottom: 0.5rem;
    }
}

/* Responsive Cards */
@media (max-width: 768px) {
    .card {
        padding: 15px;
    }

    .card-title {
        font-size: 1.2rem;
    }
}

@media (max-width: 576px) {
    .card {
        padding: 10px;
    }
}

/* Responsive Grids */
@media (max-width: 992px) {
    .grid-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .grid-container {
        grid-template-columns: 1fr;
    }
}