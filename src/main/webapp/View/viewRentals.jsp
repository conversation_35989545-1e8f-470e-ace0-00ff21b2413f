<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%@ page import="Model.RentalDetails" %>
<%@ page import="java.util.List" %>
<%@ page import="java.time.format.DateTimeFormatter" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null) {
        request.setAttribute("errorMessage", "Please login first to access your rentals");
        request.getRequestDispatcher("LogIn.jsp").forward(request, response);
        return;
    }

    List<RentalDetails> rentals = (List<RentalDetails>) request.getAttribute("rentals");
    String errorMessage = (String) request.getAttribute("errorMessage");
    String successMessage = (String) request.getAttribute("successMessage");

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy");
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Rentals - GyanKunja</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/responsive.css">
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --accent: #b388ff;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --input-bg: #f0f4ff;
            --input-border: #e0e7ff;
            --text-main: #222;
            --text-light: #666;
            --success: #4CAF50;
            --warning: #FF9800;
            --danger: #F44336;
            --info: #2196F3;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Navbar styles are in header.jsp */

        .main-content {
            max-width: 1200px;
            margin: 30px auto 50px;
            padding: 40px;
            background: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            flex: 1;
        }

        .page-title {
            font-size: 2rem;
            color: var(--primary);
            margin-bottom: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .back-button {
            padding: 0.5rem 1rem;
            background: var(--input-bg);
            color: var(--primary);
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            transition: all 0.3s;
        }

        .back-button:hover {
            background: var(--primary);
            color: var(--white);
        }

        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }

        .alert-success {
            background-color: rgba(76, 175, 80, 0.1);
            color: var(--success);
            border: 1px solid var(--success);
        }

        .alert-error {
            background-color: rgba(244, 67, 54, 0.1);
            color: var(--danger);
            border: 1px solid var(--danger);
        }

        .rentals-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1.5rem;
        }

        .rentals-table th,
        .rentals-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--input-border);
        }

        .rentals-table th {
            background-color: var(--input-bg);
            color: var(--primary);
            font-weight: 600;
        }

        .rentals-table tr:hover {
            background-color: var(--input-bg);
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            display: inline-block;
        }

        .status-pending {
            background-color: rgba(255, 152, 0, 0.1);
            color: var(--warning);
            border: 1px solid var(--warning);
        }

        .status-active {
            background-color: rgba(76, 175, 80, 0.1);
            color: var(--success);
            border: 1px solid var(--success);
        }

        .status-completed {
            background-color: rgba(33, 150, 243, 0.1);
            color: var(--info);
            border: 1px solid var(--info);
        }

        .status-cancelled {
            background-color: rgba(244, 67, 54, 0.1);
            color: var(--danger);
            border: 1px solid var(--danger);
        }

        .action-button {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            text-decoration: none;
            transition: all 0.3s;
            display: inline-block;
            margin-right: 0.5rem;
        }

        .btn-edit {
            background-color: var(--primary);
            color: var(--white);
        }

        .btn-edit:hover {
            background-color: #4a5bd9;
        }

        .btn-view {
            background-color: var(--info);
            color: var(--white);
        }

        .btn-view:hover {
            background-color: #0b7dda;
        }

        .no-rentals {
            text-align: center;
            padding: 2rem;
            background-color: var(--input-bg);
            border-radius: 8px;
            margin-top: 1.5rem;
        }

        .no-rentals i {
            font-size: 3rem;
            color: var(--text-light);
            margin-bottom: 1rem;
        }

        .no-rentals p {
            color: var(--text-light);
            margin-bottom: 1rem;
        }

        .btn-rent {
            background-color: var(--primary);
            color: var(--white);
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            text-decoration: none;
            transition: all 0.3s;
            display: inline-block;
        }

        .btn-rent:hover {
            background-color: #4a5bd9;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <jsp:include page="header.jsp" />

    <div class="main-content">
        <div class="page-title">
            <h1>My Rentals</h1>
            <a href="${pageContext.request.contextPath}/View/UserDashboard.jsp" class="back-button">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>

        <% if (errorMessage != null && !errorMessage.isEmpty()) { %>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> <%= errorMessage %>
            </div>
        <% } %>

        <% if (successMessage != null && !successMessage.isEmpty()) { %>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <%= successMessage %>
            </div>
        <% } %>

        <% if (rentals != null && !rentals.isEmpty()) { %>
            <table class="rentals-table">
                <thead>
                    <tr>
                        <th>Book Title</th>
                        <th>Rental Date</th>
                        <th>Return Date</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <% for (RentalDetails rental : rentals) { %>
                        <tr>
                            <td><%= rental.getBookTitle() != null ? rental.getBookTitle() : "Book #" + rental.getBookId() %></td>
                            <td><%= rental.getRentalDate().format(formatter) %></td>
                            <td><%= rental.getReturnDate().format(formatter) %></td>
                            <td>Rs. <%= rental.getRentalAmount() %></td>
                            <td>
                                <% if ("PENDING".equals(rental.getRentalStatus())) { %>
                                    <span class="status-badge status-pending">Pending</span>
                                <% } else if ("ACTIVE".equals(rental.getRentalStatus())) { %>
                                    <span class="status-badge status-active">Active</span>
                                <% } else if ("COMPLETED".equals(rental.getRentalStatus())) { %>
                                    <span class="status-badge status-completed">Completed</span>
                                <% } else if ("CANCELLED".equals(rental.getRentalStatus())) { %>
                                    <span class="status-badge status-cancelled">Cancelled</span>
                                <% } %>
                            </td>
                            <td>
                                <% if ("PENDING".equals(rental.getRentalStatus())) { %>
                                    <a href="${pageContext.request.contextPath}/edit-rental/<%= rental.getRentalDetailId() %>" class="action-button btn-edit">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                <% } %>
                                <a href="${pageContext.request.contextPath}/view-rental/<%= rental.getRentalDetailId() %>" class="action-button btn-view">
                                    <i class="fas fa-eye"></i> View
                                </a>
                            </td>
                        </tr>
                    <% } %>
                </tbody>
            </table>
        <% } else { %>
            <div class="no-rentals">
                <i class="fas fa-book"></i>
                <h3>No Rentals Found</h3>
                <p>You haven't rented any books yet.</p>
                <a href="${pageContext.request.contextPath}/index.jsp" class="btn-rent">
                    <i class="fas fa-book"></i> Browse Books
                </a>
            </div>
        <% } %>
    </div>

    <jsp:include page="footer.jsp" />
</body>
</html>
