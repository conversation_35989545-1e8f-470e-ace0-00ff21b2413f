<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="${pageContext.request.contextPath}/css/responsive.css">
  <style>
    :root {
      --primary: #5b6dfa;
      --secondary: #34c9c9;
      --accent: #b388ff;
      --background: #f7faff;
      --white: #fff;
      --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
      --radius: 18px;
      --input-bg: #f0f4ff;
      --input-border: #e0e7ff;
      --text-main: #222;
      --text-light: #666;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Poppins', sans-serif;
    }

    html, body {
      height: 100%;
    }

    body {
      background: var(--background);
      color: var(--text-main);
      line-height: 1.6;
      display: flex;
      flex-direction: column;
      min-height: 100vh;
    }

    .main-content {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(120deg, var(--primary) 0%, var(--secondary) 100%);
      padding: 150px 30px 80px 30px;
    }

    /* Navbar Styles */
    .navbar {
      background: var(--white);
      position: fixed;
      width: 100%;
      top: 0;
      z-index: 1000;
      box-shadow: var(--shadow);
    }

    .nav-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      padding: 1rem 5%;
      border-bottom: 1px solid var(--input-border);
    }

    .nav-bottom {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0.5rem 5%;
    }

    .nav-links {
      display: flex;
      gap: 2rem;
      align-items: center;
    }

    .nav-links a {
      color: var(--text-main);
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      transition: all 0.3s;
    }

    .nav-links a:hover {
      color: var(--primary);
      background: var(--input-bg);
    }

    .nav-links a.active {
      color: var(--primary);
      background: var(--input-bg);
    }

    .logo-section {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .search-section {
      display: flex;
      align-items: center;
      gap: 2rem;
    }

    .search-container {
      display: flex;
      gap: 0.5rem;
    }

    .search-input {
      padding: 0.5rem 1rem;
      border: 1px solid var(--input-border);
      border-radius: 8px;
      width: 300px;
    }

    .search-button {
      background: var(--primary);
      color: var(--white);
      border: none;
      padding: 0.5rem 1.5rem;
      border-radius: 8px;
      cursor: pointer;
    }

    .auth-buttons {
      display: flex;
      gap: 1rem;
    }

    .nav-link {
      color: var(--text-main);
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      transition: all 0.3s;
    }

    .login-btn {
      background: var(--primary);
      color: var(--white);
    }

    .register-btn {
      background: var(--accent);
      color: var(--white);
    }

    .nav-link:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow);
    }

    /* Register Page Styles */
    .register-card {
      background: var(--white);
      border-radius: var(--radius);
      box-shadow: var(--shadow);
      padding: 2.5rem 2.2rem 2rem 2.2rem;
      max-width: 420px;
      width: 100%;
      margin: 2rem;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .register-title {
      font-size: 2.1rem;
      color: var(--primary);
      font-weight: 700;
      margin-bottom: 0.5rem;
      letter-spacing: 0.4px;
    }

    .register-desc {
      color: var(--text-light);
      font-size: 1.05rem;
      margin-bottom: 2rem;
      text-align: center;
    }

    .register-form {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 1.2rem;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 0.4rem;
    }

    .form-group label {
      font-weight: 500;
      color: var(--text-main);
      font-size: 1rem;
    }

    .form-group input[type="text"], .form-group input[type="email"],
    .form-group input[type="tel"], .form-group input[type="password"] {
      padding: 0.7rem 1rem;
      border: 1.5px solid var(--input-border);
      border-radius: 8px;
      font-size: 1rem;
      background: var(--input-bg);
      color: var(--text-main);
      outline: none;
      transition: border-color 0.2s, box-shadow 0.2s;
    }

    .form-group input:focus {
      border-color: var(--primary);
      box-shadow: 0 0 0 2px #5b6dfa22;
    }

    .form-check {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-top: -0.5rem;
    }

    .form-check label {
      margin: 0;
      color: var(--text-light);
      font-weight: 400;
      font-size: 0.97rem;
    }

    .register-submit {
      background: linear-gradient(90deg, var(--primary) 60%, var(--accent) 100%);
      color: var(--white);
      border: none;
      padding: 0.9rem 0;
      border-radius: 8px;
      font-size: 1.13rem;
      font-weight: 600;
      cursor: pointer;
      box-shadow: 0 2px 8px #5b6dfa22;
      margin-top: 0.5rem;
      transition: background 0.2s, box-shadow 0.2s, transform 0.1s;
    }

    .register-submit:hover {
      background: linear-gradient(90deg, var(--accent) 0%, var(--primary) 100%);
      transform: translateY(-2px) scale(1.025);
      box-shadow: 0 6px 20px #5b6dfa33;
    }

    .error-message {
      background-color: #ffebee;
      color: #c62828;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
      text-align: center;
      font-weight: 500;
    }

    .success-message {
      background-color: #e8f5e9;
      color: #2e7d32;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
      text-align: center;
      font-weight: 500;
    }

    /* Add new styles for password validation */
    .password-match {
      color: #4CAF50;
      font-size: 0.85rem;
      margin-top: 0.2rem;
    }

    .password-mismatch {
      color: #ff4444;
      font-size: 0.85rem;
      margin-top: 0.2rem;
    }

    @media (max-width: 600px) {
      .search-container {
        display: none;
      }

      .register-card {
        padding: 1.4rem 0.7rem 1.2rem 0.7rem;
      }

      .register-title {
        font-size: 1.5rem;
      }
    }

    /* Footer Styles */
    .footer {
      background: var(--white);
      padding: 2rem 5%;
      border-top: 1px solid var(--input-border);
      margin-top: auto;
    }

    .footer-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      gap: 4rem;
      padding-bottom: 2rem;
    }

    .footer-left {
      max-width: 400px;
    }

    .footer-logo {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 1rem;
    }

    .footer-logo img {
      height: 30px;
    }

    .footer-logo h2 {
      font-size: 1.25rem;
      color: var(--primary);
      font-weight: 600;
    }

    .footer-description {
      color: var(--text-light);
      font-size: 0.95rem;
      line-height: 1.5;
      margin-bottom: 1rem;
    }

    .footer-right {
      display: flex;
      gap: 4rem;
    }

    .footer-section {
      min-width: 140px;
    }

    .footer-section h3 {
      color: var(--primary);
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid var(--input-border);
    }

    .footer-links {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .footer-links a {
      color: var(--text-light);
      text-decoration: none;
      font-size: 0.95rem;
      transition: all 0.3s ease;
    }

    .footer-links a:hover {
      color: var(--primary);
      transform: translateX(5px);
    }

    .social-links {
      display: flex;
      gap: 1rem;
      margin-top: 1rem;
    }

    .social-links a {
      color: var(--text-light);
      text-decoration: none;
      font-size: 1.1rem;
      transition: all 0.3s ease;
      padding: 0.5rem;
      border-radius: 50%;
      background: var(--input-bg);
    }

    .social-links a:hover {
      color: var(--primary);
      background: var(--primary);
      color: var(--white);
      transform: translateY(-3px);
    }

    .footer-bottom {
      max-width: 1200px;
      margin: 0 auto;
      padding-top: 1rem;
      border-top: 1px solid var(--input-border);
      text-align: center;
      color: var(--text-light);
      font-size: 0.9rem;
    }

    @media (max-width: 768px) {
      .footer-content {
        flex-direction: column;
        gap: 2rem;
      }

      .footer-right {
        flex-wrap: wrap;
        gap: 2rem;
      }

      .footer-section {
        min-width: 120px;
      }
    }
  </style>
  <script>
    function validatePassword() {
      const password = document.getElementById('password').value;
      const confirmPassword = document.getElementById('confirmPassword').value;
      const message = document.getElementById('passwordMessage');
      const submitButton = document.querySelector('.register-submit');

      if (password === confirmPassword) {
        message.textContent = 'Passwords match!';
        message.className = 'password-match';
        submitButton.disabled = false;
      } else {
        message.textContent = 'Passwords do not match!';
        message.className = 'password-mismatch';
        submitButton.disabled = true;
      }
    }
  </script>
    <title>Register Page</title>
</head>
<body>
<nav class="navbar">
  <div class="nav-top">
    <div class="logo-section">
      <img src="${pageContext.request.contextPath}/assets/images/books/Gyankunj.png" alt="GyanKunja Logo" style="height: 40px; width: auto;">
      <h1 class="website-name">GyanKunja</h1>
    </div>
    <div class="search-section">
      <div class="search-container">
        <input type="text" class="search-input" placeholder="Search...">
        <button class="search-button">Search</button>
      </div>
      <div class="auth-buttons">
        <a href="${pageContext.request.contextPath}/View/LogIn.jsp" class="nav-link login-btn">Login</a>
        <a href="${pageContext.request.contextPath}/ForRegister" class="nav-link register-btn">Register</a>
      </div>
    </div>
  </div>
  <button class="mobile-menu-toggle" id="mobileMenuToggle">
    <i class="fas fa-bars"></i>
  </button>
  <div class="nav-bottom" id="navBottom">
    <div class="nav-links">
      <a href="${pageContext.request.contextPath}/index.jsp" class="nav-link">Home</a>
      <a href="${pageContext.request.contextPath}/View/category.jsp" class="nav-link">Categories</a>
      <a href="${pageContext.request.contextPath}/View/UserDashboard.jsp" class="nav-link">User Dashboard</a>
      <a href="${pageContext.request.contextPath}/View/Contact.jsp" class="nav-link">Contact Us</a>
      <a href="${pageContext.request.contextPath}/View/about.jsp" class="nav-link">About Us</a>
    </div>
  </div>
</nav>

<main class="main-content">
  <div class="register-card">
    <h2 class="register-title">Create Your Account</h2>
    <div class="register-desc">Sign up to access a world of knowledge and resources!</div>

    <% if (request.getAttribute("errorMessage") != null) { %>
      <div class="error-message">
        <%= request.getAttribute("errorMessage") %>
      </div>
    <% } %>

    <form class="register-form" action="${pageContext.request.contextPath}/ForRegister" method="POST">
      <div class="form-group">
        <label for="name">Name</label>
        <input type="text" id="name" name="username" placeholder="Enter your name" required>
      </div>
      <div class="form-group">
        <label for="email">Email Address</label>
        <input type="email" id="email" name="email" placeholder="Enter your email" required>
      </div>
      <div class="form-group">
        <label for="password">Password</label>
        <input type="password" id="password" name="password" placeholder="Create a password" required onkeyup="validatePassword()">
      </div>
      <div class="form-group">
        <label for="confirmPassword">Confirm Password</label>
        <input type="password" id="confirmPassword" name="confirmPassword" placeholder="Confirm your password" required onkeyup="validatePassword()">
        <div id="passwordMessage"></div>
      </div>
      <% if (request.getAttribute("error") != null) { %>
        <div class="error-message">
          <%= request.getAttribute("error") %>
        </div>
      <% } %>
      <button type="submit" class="register-submit" >Register</button>
    </form>
    <div class="login-footer" style="margin-top: 1rem; text-align: center;">
      Already have an account? <a href="${pageContext.request.contextPath}/View/LogIn.jsp" class="create-account-link">Login</a>
    </div>
  </div>
</main>

<jsp:include page="footer.jsp" />

<script>
  // Mobile menu toggle
  const mobileMenuToggle = document.getElementById('mobileMenuToggle');
  const navBottom = document.getElementById('navBottom');

  mobileMenuToggle.addEventListener('click', function() {
    navBottom.classList.toggle('active');
  });

  // Close mobile menu when clicking outside
  document.addEventListener('click', function(event) {
    if (!navBottom.contains(event.target) && !mobileMenuToggle.contains(event.target)) {
      navBottom.classList.remove('active');
    }
  });
</script>
</body>
</html>
