<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%@ page import="java.time.LocalDate" %>
<%@ page import="java.time.format.DateTimeFormatter" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
        response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
        return;
    }

    LocalDate startDate = (LocalDate) request.getAttribute("startDate");
    LocalDate endDate = (LocalDate) request.getAttribute("endDate");

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Reports - GyanKunja</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        .report-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .report-grid {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 20px;
        }

        .report-card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .report-card-content {
            flex: 1;
        }

        .report-card h3 {
            margin-top: 0;
            margin-bottom: 5px;
            color: #5b6dfa;
            font-size: 1.2rem;
        }

        .report-card p {
            color: #666;
            margin-bottom: 0;
            font-size: 0.9rem;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            white-space: nowrap;
        }

        .btn-primary {
            background-color: #5b6dfa;
            color: white;
            border: none;
        }

        .date-filter {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .date-filter label {
            font-weight: bold;
        }

        .date-filter input[type="date"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .date-filter button {
            padding: 8px 15px;
            background-color: #5b6dfa;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .admin-header h1 {
            margin-bottom: 10px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Include header and navigation -->
        <jsp:include page="adminHeader.jsp" />

        <div class="main-content">
            <div class="admin-header">
                <h1>Reports Dashboard</h1>
            </div>

            <div class="report-container">
                <h2>Generate Reports</h2>
                <p>Select a date range and generate various reports to analyze library performance.</p>

                <form action="${pageContext.request.contextPath}/admin/reports" method="get" class="date-filter">
                    <label for="startDate">Start Date:</label>
                    <input type="date" id="startDate" name="startDate" value="<%= startDate.format(formatter) %>">

                    <label for="endDate">End Date:</label>
                    <input type="date" id="endDate" name="endDate" value="<%= endDate.format(formatter) %>">

                    <button type="submit">Apply Filter</button>
                </form>

                <div class="report-grid">
                    <div class="report-card">
                        <div class="report-card-content">
                            <h3>User Activity Report</h3>
                            <p>View most active users and their rental patterns</p>
                        </div>
                        <a href="${pageContext.request.contextPath}/admin/reports/user-activity?startDate=<%= startDate.format(formatter) %>&endDate=<%= endDate.format(formatter) %>" class="btn btn-primary">View Report</a>
                    </div>

                    <div class="report-card">
                        <div class="report-card-content">
                            <h3>Popular Books Report</h3>
                            <p>See which books are most frequently rented</p>
                        </div>
                        <a href="${pageContext.request.contextPath}/admin/reports/popular-books?startDate=<%= startDate.format(formatter) %>&endDate=<%= endDate.format(formatter) %>" class="btn btn-primary">View Report</a>
                    </div>

                    <div class="report-card">
                        <div class="report-card-content">
                            <h3>Rental Activity Report</h3>
                            <p>Overview of rental statistics and revenue</p>
                        </div>
                        <a href="${pageContext.request.contextPath}/admin/reports/rental-activity?startDate=<%= startDate.format(formatter) %>&endDate=<%= endDate.format(formatter) %>" class="btn btn-primary">View Report</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Include footer -->
        <jsp:include page="footer.jsp" />
    </div>

    <script>
        // JavaScript for any interactive elements
    </script>
</body>
</html>