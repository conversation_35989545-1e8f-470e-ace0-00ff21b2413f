<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
        response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports Dashboard - GyanKunja Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --text-main: #222;
            --text-light: #666;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .main-content {
            padding: 20px 0;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 1.8rem;
            color: #333;
            margin: 0;
        }

        .report-card {
            background: var(--white);
            border-radius: var(--radius);
            padding: 20px;
            box-shadow: var(--shadow);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .report-card-content {
            flex: 1;
        }

        .report-card h3 {
            font-size: 1.2rem;
            color: var(--primary);
            margin: 0 0 5px 0;
        }

        .report-card p {
            color: var(--text-light);
            margin: 0;
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: var(--primary);
            color: white;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .btn:hover {
            background-color: #4a5ce0;
        }
    </style>
</head>
<body>
    <!-- Include header -->
    <jsp:include page="adminHeader.jsp" />

    <div class="container">
        <div class="main-content">
            <div class="page-header">
                <h1 class="page-title">Reports Dashboard</h1>
            </div>

            <!-- Report Cards -->
            <div class="report-card">
                <div class="report-card-content">
                    <h3>User Activity Report</h3>
                    <p>View most active users and their rental patterns</p>
                </div>
                <a href="${pageContext.request.contextPath}/admin/reports/user-activity" class="btn">View Report</a>
            </div>

            <div class="report-card">
                <div class="report-card-content">
                    <h3>Popular Books Report</h3>
                    <p>See which books are most frequently rented</p>
                </div>
                <a href="${pageContext.request.contextPath}/admin/reports/popular-books" class="btn">View Report</a>
            </div>

            <div class="report-card">
                <div class="report-card-content">
                    <h3>Rental Activity Report</h3>
                    <p>Overview of rental statistics and revenue</p>
                </div>
                <a href="${pageContext.request.contextPath}/admin/reports/rental-activity" class="btn">View Report</a>
            </div>
        </div>
    </div>

    <!-- Include footer -->
    <jsp:include page="footer.jsp" />
</body>
</html>