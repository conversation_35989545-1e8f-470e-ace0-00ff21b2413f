<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%@ page import="java.util.Map" %>
<%@ page import="java.util.List" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
        response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
        return;
    }

    // Get any statistics from request attributes
    Integer totalRentals = (Integer) request.getAttribute("totalRentals");
    Integer activeRentals = (Integer) request.getAttribute("activeRentals");
    Integer completedRentals = (Integer) request.getAttribute("completedRentals");
    Double totalRevenue = (Double) request.getAttribute("totalRevenue");

    // Default values if not set
    if (totalRentals == null) totalRentals = 0;
    if (activeRentals == null) activeRentals = 0;
    if (completedRentals == null) completedRentals = 0;
    if (totalRevenue == null) totalRevenue = 0.0;

    List<Map<String, Object>> topUsers = (List<Map<String, Object>>) request.getAttribute("topUsers");
    List<Map<String, Object>> topBooks = (List<Map<String, Object>>) request.getAttribute("topBooks");
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports Dashboard - GyanKunja Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --accent: #b388ff;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --text-main: #222;
            --text-light: #666;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
            padding-top: 60px; /* Space for fixed header */
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .main-content {
            padding: 20px 0;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 1.8rem;
            color: #333;
            margin: 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: var(--white);
            border-radius: var(--radius);
            padding: 20px;
            box-shadow: var(--shadow);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card h3 {
            color: var(--text-light);
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .stat-card p {
            color: var(--primary);
            font-size: 1.8rem;
            font-weight: 600;
            margin: 0;
        }

        .report-section {
            background: var(--white);
            border-radius: var(--radius);
            padding: 20px;
            box-shadow: var(--shadow);
            margin-bottom: 30px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .section-title {
            font-size: 1.2rem;
            color: #333;
            margin: 0;
        }

        .btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: var(--primary);
            color: white;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .btn:hover {
            background-color: #4a5ce0;
        }

        .chart-container {
            height: 300px;
            margin-bottom: 20px;
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 500;
            color: #333;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        .empty-state {
            text-align: center;
            padding: 30px;
            color: var(--text-light);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #ddd;
        }

        .empty-state p {
            font-size: 1rem;
            margin: 0;
        }
    </style>
</head>
<body>
    <!-- Include header -->
    <jsp:include page="adminHeader.jsp" />

    <div class="container">
        <div class="main-content">
            <div class="page-header">
                <h1 class="page-title">Reports Dashboard</h1>
            </div>

            <!-- Stats Overview -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>Total Rentals</h3>
                    <p><%= totalRentals %></p>
                </div>
                <div class="stat-card">
                    <h3>Active Rentals</h3>
                    <p><%= activeRentals %></p>
                </div>
                <div class="stat-card">
                    <h3>Completed Rentals</h3>
                    <p><%= completedRentals %></p>
                </div>
                <div class="stat-card">
                    <h3>Total Revenue</h3>
                    <p>₹<%= String.format("%.2f", totalRevenue) %></p>
                </div>
            </div>

            <!-- Rental Activity Report -->
            <div class="report-section">
                <div class="section-header">
                    <h2 class="section-title">Rental Activity Report</h2>
                    <a href="${pageContext.request.contextPath}/admin/reports/rental-activity" class="btn">View Full Report</a>
                </div>

                <div class="chart-container">
                    <canvas id="rentalChart"></canvas>
                </div>
            </div>

            <!-- User Activity Report -->
            <div class="report-section">
                <div class="section-header">
                    <h2 class="section-title">Top Active Users</h2>
                    <a href="${pageContext.request.contextPath}/admin/reports/user-activity" class="btn">View Full Report</a>
                </div>

                <div class="table-container">
                    <% if (topUsers != null && !topUsers.isEmpty()) { %>
                        <table>
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Username</th>
                                    <th>Email</th>
                                    <th>Rental Count</th>
                                    <th>Total Spent</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% int rank = 1; %>
                                <% for (Map<String, Object> user : topUsers) { %>
                                    <tr>
                                        <td><%= rank++ %></td>
                                        <td><%= user.get("username") %></td>
                                        <td><%= user.get("email") %></td>
                                        <td><%= user.get("rentalCount") %></td>
                                        <td>₹<%= String.format("%.2f", user.get("totalSpent")) %></td>
                                    </tr>
                                <% } %>
                            </tbody>
                        </table>
                    <% } else { %>
                        <div class="empty-state">
                            <i class="fas fa-users"></i>
                            <p>No user activity data available</p>
                        </div>
                    <% } %>
                </div>
            </div>

            <!-- Popular Books Report -->
            <div class="report-section">
                <div class="section-header">
                    <h2 class="section-title">Most Popular Books</h2>
                    <a href="${pageContext.request.contextPath}/admin/reports/popular-books" class="btn">View Full Report</a>
                </div>

                <div class="table-container">
                    <% if (topBooks != null && !topBooks.isEmpty()) { %>
                        <table>
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Book Title</th>
                                    <th>Author</th>
                                    <th>Rental Count</th>
                                    <th>Revenue</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% int rank = 1; %>
                                <% for (Map<String, Object> book : topBooks) { %>
                                    <tr>
                                        <td><%= rank++ %></td>
                                        <td><%= book.get("title") %></td>
                                        <td><%= book.get("author") %></td>
                                        <td><%= book.get("rentalCount") %></td>
                                        <td>₹<%= String.format("%.2f", book.get("revenue")) %></td>
                                    </tr>
                                <% } %>
                            </tbody>
                        </table>
                    <% } else { %>
                        <div class="empty-state">
                            <i class="fas fa-books"></i>
                            <p>No book popularity data available</p>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>
    </div>

    <!-- Include footer -->
    <jsp:include page="footer.jsp" />

    <script>
        // Sample data for the rental chart
        const rentalData = {
            labels: ['Active', 'Completed', 'Pending'],
            datasets: [{
                label: 'Rental Status',
                data: [<%= activeRentals %>, <%= completedRentals %>, <%= totalRentals - activeRentals - completedRentals %>],
                backgroundColor: ['#36A2EB', '#4BC0C0', '#FFCD56'],
                hoverOffset: 4
            }]
        };

        // Create rental chart
        const rentalCtx = document.getElementById('rentalChart').getContext('2d');
        const rentalChart = new Chart(rentalCtx, {
            type: 'doughnut',
            data: rentalData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    title: {
                        display: true,
                        text: 'Rental Status Distribution',
                        font: { size: 16 }
                    }
                }
            }
        });
    </script>
</body>
</html>