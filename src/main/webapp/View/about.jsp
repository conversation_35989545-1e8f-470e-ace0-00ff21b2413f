<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%
    User user = (User) session.getAttribute("user");
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us | GyanKunja</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --accent: #b388ff;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --input-bg: #f0f4ff;
            --input-border: #e0e7ff;
            --text-main: #222;
            --text-light: #666;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Navbar Styles */
        .navbar {
            background: var(--white);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow);
        }

        .nav-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem 5%;
            border-bottom: 1px solid var(--input-border);
            gap: 3rem;
        }

        .nav-bottom {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0.5rem 5%;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--text-main);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary);
            background: var(--input-bg);
        }

        .nav-links a.active {
            color: var(--primary);
            background: var(--input-bg);
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .search-section {
            display: flex;
            align-items: center;
            gap: 2rem;
            width: 100%;
            justify-content: flex-end;
            margin-left: 2.5rem;
        }

        .search-container {
            display: flex;
            gap: 0.5rem;
        }

        .search-input {
            padding: 0.5rem 1rem;
            border: 1px solid var(--input-border);
            border-radius: 8px;
            width: 300px;
        }

        .search-button {
            background: var(--primary);
            color: var(--white);
            border: none;
            padding: 0.5rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
        }

        .nav-link {
            color: var(--text-main);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .login-btn {
            background: var(--primary);
            color: var(--white);
        }

        .register-btn {
            background: var(--accent);
            color: var(--white);
        }

        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        /* Main Content Styles */
        .main-content {
            padding-top: 150px;
            padding-bottom: 80px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* About Page Styles */
        .about-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .about-header h1 {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .about-header p {
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto;
        }

        .about-section {
            background: var(--white);
            border-radius: var(--radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .about-section h2 {
            font-size: 1.8rem;
            color: var(--primary);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--primary);
        }

        .about-section p {
            color: var(--text-light);
            margin-bottom: 1rem;
        }

        .mission-vision {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .mission-card, .vision-card {
            background: var(--white);
            border-radius: var(--radius);
            padding: 2rem;
            box-shadow: var(--shadow);
            text-align: center;
        }

        .mission-card i, .vision-card i {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .mission-card h3, .vision-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--primary);
        }

        .team-section {
            margin-top: 3rem;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .team-member {
            background: var(--white);
            border-radius: var(--radius);
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .member-image {
            width: 100%;
            height: 250px;
        }

        .member-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .member-info {
            padding: 1.5rem;
            text-align: center;
        }

        .member-info h3 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        .member-info p {
            color: var(--text-light);
            margin-bottom: 1rem;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        .social-links a {
            color: var(--primary);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.3s;
        }

        .social-links a:hover {
            transform: translateY(-2px);
            color: var(--accent);
        }

        /* Footer Styles */
        .footer {
            background: var(--white);
            padding: 2rem 5%;
            border-top: 1px solid var(--input-border);
            margin-top: auto;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            gap: 4rem;
            padding-bottom: 2rem;
        }

        .footer-left {
            max-width: 400px;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .footer-logo img {
            height: 30px;
        }

        .footer-logo h2 {
            font-size: 1.25rem;
            color: var(--primary);
            font-weight: 600;
        }

        .footer-description {
            color: var(--text-light);
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .footer-right {
            display: flex;
            gap: 4rem;
        }

        .footer-section {
            min-width: 140px;
        }

        .footer-section h3 {
            color: var(--primary);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--input-border);
        }

        .footer-links {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .footer-links a {
            color: var(--text-light);
            text-decoration: none;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .footer-links a:hover {
            color: var(--primary);
            transform: translateX(5px);
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .social-links a {
            color: var(--text-light);
            text-decoration: none;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            padding: 0.5rem;
            border-radius: 50%;
            background: var(--input-bg);
        }

        .social-links a:hover {
            color: var(--primary);
            background: var(--primary);
            color: var(--white);
            transform: translateY(-3px);
        }

        .footer-bottom {
            max-width: 1200px;
            margin: 0 auto;
            padding-top: 1rem;
            border-top: 1px solid var(--input-border);
            text-align: center;
            color: var(--text-light);
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .mission-vision {
                grid-template-columns: 1fr;
            }

            .nav-top {
                flex-direction: column;
                gap: 1rem;
            }

            .search-section {
                flex-direction: column;
                width: 100%;
            }

            .search-container {
                width: 100%;
            }

            .search-input {
                width: 100%;
            }

            .footer-content {
                flex-direction: column;
                gap: 2rem;
            }

            .footer-right {
                flex-wrap: wrap;
                gap: 2rem;
            }

            .footer-section {
                min-width: 120px;
            }
        }

        .website-name {
            font-size: 1.5rem;
            font-weight: 700;
        }
    </style>
</head>
<body>
    <header class="navbar">
        <div class="nav-top">
            <div class="logo-section">
                <img src="${pageContext.request.contextPath}/assets/images/books/Gyankunj.png" alt="GyanKunja Logo" style="height: 40px; width: auto;">
                <h1 class="website-name">GyanKunja</h1>
            </div>
            <div class="search-section">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search...">
                    <button class="search-button">Search</button>
                </div>
                <div class="auth-buttons">
                    <% if (user == null) { %>
                        <a href="${pageContext.request.contextPath}/View/LogIn.jsp" class="nav-link login-btn">Login</a>
                        <a href="${pageContext.request.contextPath}/View/Register.jsp" class="nav-link register-btn">Register</a>
                    <% } else { %>
                        <div class="user-info">
                            <span class="username"><%= user.getUserName() %></span>
                            <a href="${pageContext.request.contextPath}/ForLogOut" class="nav-link logout-btn">
                                <i class="fas fa-sign-out-alt"></i>
                            </a>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>
        <div class="nav-bottom">
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/index.jsp" class="nav-link">Home</a>
                <a href="${pageContext.request.contextPath}/View/category.jsp" class="nav-link">Categories</a>
                <a href="${pageContext.request.contextPath}/View/UserDashboard.jsp" class="nav-link">User Dashboard</a>
                <a href="${pageContext.request.contextPath}/View/Contact.jsp" class="nav-link">Contact Us</a>
                <a href="${pageContext.request.contextPath}/View/about.jsp" class="nav-link active">About Us</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container">
            <div class="about-header">
                <h1>About GyanKunja</h1>
                <p>Discover our story, mission, and the team behind your favorite book rental platform.</p>
            </div>

            <div class="about-section">
                <h2>Our Story</h2>
                <p>GyanKunja was founded in 2024 with a simple yet powerful vision: to make knowledge accessible to everyone through an extensive collection of books. What started as a small local library has now grown into a thriving online platform serving book lovers across the globe.</p>
                <p>Our journey has been guided by the belief that books have the power to transform lives. We're committed to providing a seamless experience for our users, whether they're looking for the latest bestsellers, academic resources, or timeless classics.</p>
            </div>

            <div class="mission-vision">
                <div class="mission-card">
                    <i class="fas fa-bullseye"></i>
                    <h3>Our Mission</h3>
                    <p>To democratize access to knowledge by providing an affordable and convenient book rental service that connects readers with their next great read.</p>
                </div>
                <div class="vision-card">
                    <i class="fas fa-eye"></i>
                    <h3>Our Vision</h3>
                    <p>To become the world's leading platform for book rentals, fostering a global community of readers and lifelong learners.</p>
                </div>
            </div>

            <div class="about-section">
                <h2>Why Choose Us</h2>
                <p>At GyanKunja, we're more than just a book rental service. We're your partner in the journey of knowledge and discovery. Here's what sets us apart:</p>
                <ul style="list-style: none; margin-top: 1rem;">
                    <li style="margin-bottom: 0.5rem; display: flex; align-items: flex-start; gap: 0.5rem;">
                        <i class="fas fa-check" style="color: var(--primary);"></i>
                        <span>Extensive collection of books across all genres</span>
                    </li>
                    <li style="margin-bottom: 0.5rem; display: flex; align-items: flex-start; gap: 0.5rem;">
                        <i class="fas fa-check" style="color: var(--primary);"></i>
                        <span>Affordable rental plans with flexible durations</span>
                    </li>
                    <li style="margin-bottom: 0.5rem; display: flex; align-items: flex-start; gap: 0.5rem;">
                        <i class="fas fa-check" style="color: var(--primary);"></i>
                        <span>Fast and reliable delivery service</span>
                    </li>
                    <li style="margin-bottom: 0.5rem; display: flex; align-items: flex-start; gap: 0.5rem;">
                        <i class="fas fa-check" style="color: var(--primary);"></i>
                        <span>User-friendly platform with personalized recommendations</span>
                    </li>
                    <li style="margin-bottom: 0.5rem; display: flex; align-items: flex-start; gap: 0.5rem;">
                        <i class="fas fa-check" style="color: var(--primary);"></i>
                        <span>Dedicated customer support team</span>
                    </li>
                </ul>
            </div>

            <div class="team-section">
                <h2 class="section-title">Meet Our Team</h2>
                <div class="team-grid">
                    <div class="team-member">
                        <div class="member-image">
                            <img src="assets/images/team/team-1.jpg" alt="Team Member">
                        </div>
                        <div class="member-info">
                            <h3>John Smith</h3>
                            <p>Founder & CEO</p>
                            <div class="social-links">
                                <a href="#"><i class="fab fa-linkedin"></i></a>
                                <a href="#"><i class="fab fa-twitter"></i></a>
                                <a href="#"><i class="fab fa-github"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="team-member">
                        <div class="member-image">
                            <img src="assets/images/team/team-2.jpg" alt="Team Member">
                        </div>
                        <div class="member-info">
                            <h3>Sarah Johnson</h3>
                            <p>Head of Operations</p>
                            <div class="social-links">
                                <a href="#"><i class="fab fa-linkedin"></i></a>
                                <a href="#"><i class="fab fa-twitter"></i></a>
                                <a href="#"><i class="fab fa-github"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="team-member">
                        <div class="member-image">
                            <img src="assets/images/team/team-3.jpg" alt="Team Member">
                        </div>
                        <div class="member-info">
                            <h3>Michael Chen</h3>
                            <p>Technical Lead</p>
                            <div class="social-links">
                                <a href="#"><i class="fab fa-linkedin"></i></a>
                                <a href="#"><i class="fab fa-twitter"></i></a>
                                <a href="#"><i class="fab fa-github"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <jsp:include page="footer.jsp" />
</body>
</html>