<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%@ page import="Model.RentalDetails" %>
<%@ page import="java.time.format.DateTimeFormatter" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null) {
        request.setAttribute("errorMessage", "Please login first to access your rentals");
        request.getRequestDispatcher("LogIn.jsp").forward(request, response);
        return;
    }

    RentalDetails rental = (RentalDetails) request.getAttribute("rental");
    if (rental == null) {
        response.sendRedirect(request.getContextPath() + "/user-rentals");
        return;
    }

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy");
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Rental | GyanKunja</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/responsive.css">
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --accent: #b388ff;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --input-bg: #f0f4ff;
            --input-border: #e0e7ff;
            --text-main: #222;
            --text-light: #666;
            --success: #4CAF50;
            --warning: #FF9800;
            --danger: #F44336;
            --info: #2196F3;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            max-width: 800px;
            margin: 30px auto 50px;
            padding: 40px;
            background: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            flex: 1;
        }

        .page-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .page-title h1 {
            font-size: 1.8rem;
            color: var(--primary);
        }

        .back-button {
            padding: 0.5rem 1rem;
            background: var(--input-bg);
            color: var(--primary);
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            transition: all 0.3s;
        }

        .back-button:hover {
            background: var(--primary);
            color: var(--white);
        }

        .rental-details {
            background: var(--input-bg);
            border-radius: var(--radius);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .detail-group {
            margin-bottom: 1.5rem;
        }

        .detail-group:last-child {
            margin-bottom: 0;
        }

        .detail-label {
            font-size: 0.9rem;
            color: var(--text-light);
            margin-bottom: 0.5rem;
            display: block;
        }

        .detail-value {
            font-size: 1.1rem;
            font-weight: 500;
        }

        .status-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-pending {
            background-color: rgba(255, 152, 0, 0.1);
            color: var(--warning);
            border: 1px solid var(--warning);
        }

        .status-active {
            background-color: rgba(76, 175, 80, 0.1);
            color: var(--success);
            border: 1px solid var(--success);
        }

        .status-completed {
            background-color: rgba(33, 150, 243, 0.1);
            color: var(--info);
            border: 1px solid var(--info);
        }

        .status-cancelled {
            background-color: rgba(244, 67, 54, 0.1);
            color: var(--danger);
            border: 1px solid var(--danger);
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            border: none;
        }

        .btn-primary {
            background: var(--primary);
            color: var(--white);
        }

        .btn-secondary {
            background: var(--input-bg);
            color: var(--text-main);
        }

        .btn-danger {
            background: var(--danger);
            color: var(--white);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 20px;
                margin: 20px auto;
            }

            .page-title {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .rental-details {
                padding: 1.5rem;
            }

            .action-buttons {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <jsp:include page="header.jsp" />

    <div class="main-content">
        <div class="page-title">
            <h1>Rental Details</h1>
            <a href="${pageContext.request.contextPath}/user-rentals" class="back-button">
                <i class="fas fa-arrow-left"></i> Back to Rentals
            </a>
        </div>

        <div class="rental-details">
            <div class="detail-group">
                <span class="detail-label">Rental ID</span>
                <span class="detail-value">#<%= rental.getRentalDetailId() %></span>
            </div>

            <div class="detail-group">
                <span class="detail-label">Book Title</span>
                <span class="detail-value"><%= rental.getBookTitle() != null ? rental.getBookTitle() : "Book #" + rental.getBookId() %></span>
            </div>

            <div class="detail-group">
                <span class="detail-label">Rental Date</span>
                <span class="detail-value"><%= rental.getRentalDate().format(formatter) %></span>
            </div>

            <div class="detail-group">
                <span class="detail-label">Return Date</span>
                <span class="detail-value"><%= rental.getReturnDate().format(formatter) %></span>
            </div>

            <% if (rental.getActualReturnDate() != null) { %>
                <div class="detail-group">
                    <span class="detail-label">Actual Return Date</span>
                    <span class="detail-value"><%= rental.getActualReturnDate().format(formatter) %></span>
                </div>
            <% } %>

            <div class="detail-group">
                <span class="detail-label">Rental Amount</span>
                <span class="detail-value">Rs. <%= rental.getRentalAmount() %></span>
            </div>

            <div class="detail-group">
                <span class="detail-label">Status</span>
                <% if ("PENDING".equals(rental.getRentalStatus())) { %>
                    <span class="status-badge status-pending">Pending</span>
                <% } else if ("ACTIVE".equals(rental.getRentalStatus())) { %>
                    <span class="status-badge status-active">Active</span>
                <% } else if ("COMPLETED".equals(rental.getRentalStatus())) { %>
                    <span class="status-badge status-completed">Completed</span>
                <% } else if ("CANCELLED".equals(rental.getRentalStatus())) { %>
                    <span class="status-badge status-cancelled">Cancelled</span>
                <% } %>
            </div>
        </div>

        <div class="action-buttons">
            <% if ("PENDING".equals(rental.getRentalStatus())) { %>
                <a href="${pageContext.request.contextPath}/edit-rental/<%= rental.getRentalDetailId() %>" class="btn btn-primary">
                    <i class="fas fa-edit"></i> Edit Rental
                </a>
                <button onclick="cancelRental(<%= rental.getRentalDetailId() %>)" class="btn btn-danger">
                    <i class="fas fa-times"></i> Cancel Rental
                </button>
            <% } %>
            <a href="${pageContext.request.contextPath}/user-rentals" class="btn btn-secondary">
                <i class="fas fa-list"></i> View All Rentals
            </a>
        </div>
    </div>

    <jsp:include page="footer.jsp" />

    <script>
        function cancelRental(rentalId) {
            if (confirm('Are you sure you want to cancel this rental?')) {
                window.location.href = '${pageContext.request.contextPath}/cancel-rental/' + rentalId;
            }
        }
    </script>
</body>
</html>
