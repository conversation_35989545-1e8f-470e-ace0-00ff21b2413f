<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%@ page import="Model.RentalDetails" %>
<%
    User user = (User) session.getAttribute("user");
    RentalDetails rental = (RentalDetails) request.getAttribute("rental");
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rental Confirmation | GyanKunja</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/responsive.css">
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --accent: #b388ff;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --input-bg: #f0f4ff;
            --input-border: #e0e7ff;
            --text-main: #222;
            --text-light: #666;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
            min-height: 100vh;
        }

        .main-content {
            padding-top: 30px;
            padding-bottom: 80px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .success-card {
            background: var(--white);
            border-radius: var(--radius);
            padding: 3rem;
            margin: 0 auto;
            max-width: 600px;
            text-align: center;
            box-shadow: var(--shadow);
        }

        .success-icon {
            font-size: 4rem;
            color: #2e7d32;
            margin-bottom: 1.5rem;
        }

        .success-title {
            font-size: 2rem;
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .success-message {
            color: var(--text-light);
            margin-bottom: 2rem;
        }

        .rental-details {
            background: var(--input-bg);
            border-radius: var(--radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: left;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--input-border);
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            color: var(--text-light);
        }

        .detail-value {
            font-weight: 500;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .btn {
            padding: 0.75rem 2rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
        }

        .btn-primary {
            background: var(--primary);
            color: var(--white);
        }

        .btn-secondary {
            background: var(--input-bg);
            color: var(--text-main);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }
    </style>
</head>
<body>
    <jsp:include page="header.jsp" />

    <div class="main-content">
        <div class="container">
            <div class="success-card">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h1 class="success-title">Rental Confirmed!</h1>
                <p class="success-message">Your book rental has been successfully processed. Here are the details of your rental:</p>

                <div class="rental-details">
                    <div class="detail-item">
                        <span class="detail-label">Rental ID:</span>
                        <span class="detail-value">#<%= rental.getRentalDetailId() %></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Rental Date:</span>
                        <span class="detail-value"><%= rental.getRentalDate() %></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Return Date:</span>
                        <span class="detail-value"><%= rental.getReturnDate() %></span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Total Amount:</span>
                        <span class="detail-value">Rs. <%= rental.getRentalAmount() %></span>
                    </div>
                </div>

                <div class="action-buttons">
                    <a href="${pageContext.request.contextPath}/View/UserDashboard.jsp" class="btn btn-primary">
                        View My Rentals
                    </a>
                    <a href="${pageContext.request.contextPath}/index.jsp" class="btn btn-secondary">
                        Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Include your footer here -->
    <jsp:include page="footer.jsp" />
</body>
</html>