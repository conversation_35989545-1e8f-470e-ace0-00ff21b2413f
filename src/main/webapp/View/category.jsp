<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%
    User user = (User) session.getAttribute("user");
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Categories | GyanKunja</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --accent: #b388ff;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --input-bg: #f0f4ff;
            --input-border: #e0e7ff;
            --text-main: #222;
            --text-light: #666;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Navbar Styles */
        .navbar {
            background: var(--white);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow);
        }

        .nav-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem 5%;
            border-bottom: 1px solid var(--input-border);
            gap: 3rem;
        }

        .nav-bottom {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0.5rem 5%;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--text-main);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary);
            background: var(--input-bg);
        }

        .nav-links a.active {
            color: var(--primary);
            background: var(--input-bg);
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .search-section {
            display: flex;
            align-items: center;
            gap: 2rem;
            width: 100%;
            justify-content: flex-end;
            margin-left: 2.5rem;
        }

        .search-container {
            display: flex;
            gap: 0.5rem;
        }

        .search-input {
            padding: 0.5rem 1rem;
            border: 1px solid var(--input-border);
            border-radius: 8px;
            width: 300px;
        }

        .search-button {
            background: var(--primary);
            color: var(--white);
            border: none;
            padding: 0.5rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
        }

        .nav-link {
            color: var(--text-main);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .login-btn {
            background: var(--primary);
            color: var(--white);
        }

        .register-btn {
            background: var(--accent);
            color: var(--white);
        }

        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        /* Main Content Styles */
        .main-content {
            padding-top: 120px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Book Card Styles */
        .book-card {
            background: var(--white);
            border-radius: var(--radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            display: flex;
            flex-direction: column;
        }

        .book-cover {
            width: 100%;
            height: 300px;
            background: var(--input-bg);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .book-cover img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            max-width: 200px;
        }

        .book-info {
            padding: 1.5rem;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .book-info h3 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            color: var(--text-main);
        }

        .book-info p {
            color: var(--text-light);
            font-size: 0.95rem;
            margin-bottom: 0.5rem;
        }

        .book-rating {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .rating-stars {
            color: #ffc107;
        }

        .rating-value {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .rent-button {
            background: var(--primary);
            color: var(--white);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            width: 100%;
            font-weight: 500;
        }

        /* Section Styles */
        section {
            margin-bottom: 3rem;
        }

        section h2 {
            font-size: 2rem;
            color: var(--text-main);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--primary);
        }

        /* Grid Layout */
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        /* Footer Styles */
        .footer {
            background: var(--white);
            padding: 2rem 5%;
            border-top: 1px solid var(--input-border);
            margin-top: auto;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            gap: 4rem;
            padding-bottom: 2rem;
        }

        .footer-left {
            max-width: 400px;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .footer-logo img {
            height: 30px;
        }

        .footer-logo h2 {
            font-size: 1.25rem;
            color: var(--primary);
            font-weight: 600;
        }

        .footer-description {
            color: var(--text-light);
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .footer-right {
            display: flex;
            gap: 4rem;
        }

        .footer-section {
            min-width: 140px;
        }

        .footer-section h3 {
            color: var(--primary);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--input-border);
        }

        .footer-links {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .footer-links a {
            color: var(--text-light);
            text-decoration: none;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .footer-links a:hover {
            color: var(--primary);
            transform: translateX(5px);
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .social-links a {
            color: var(--text-light);
            text-decoration: none;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            padding: 0.5rem;
            border-radius: 50%;
            background: var(--input-bg);
        }

        .social-links a:hover {
            color: var(--primary);
            background: var(--primary);
            color: var(--white);
            transform: translateY(-3px);
        }

        .footer-bottom {
            max-width: 1200px;
            margin: 0 auto;
            padding-top: 1rem;
            border-top: 1px solid var(--input-border);
            text-align: center;
            color: var(--text-light);
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-top {
                flex-direction: column;
                gap: 1rem;
            }

            .search-section {
                flex-direction: column;
                width: 100%;
            }

            .search-container {
                width: 100%;
            }

            .search-input {
                width: 100%;
            }

            .footer-content {
                flex-direction: column;
                gap: 2rem;
            }

            .footer-right {
                flex-wrap: wrap;
                gap: 2rem;
            }

            .footer-section {
                min-width: 120px;
            }

            .grid {
                grid-template-columns: 1fr;
            }
        }

        .website-name {
            font-size: 1.5rem;
            font-weight: 700;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'pastel-blue': '#a8d8ea',
                        'pastel-blue-dark': '#7ab8ca',
                        'pastel-blue-light': '#d4e9f2',
                    }
                }
            }
        }

        function rentBook(bookId) {
            // Check if user is logged in
            <% if (user == null) { %>
                alert('Please log in to rent a book');
                window.location.href = '${pageContext.request.contextPath}/View/LogIn.jsp';
                return;
            <% } %>

            // Redirect to rent book page
            window.location.href = '${pageContext.request.contextPath}/rent-book?id=' + bookId;
        }
    </script>
</head>
<body class="bg-gray-50">
    <jsp:include page="header.jsp" />

    <!-- Main Content -->
    <div class="main-content">
        <div class="container mx-auto px-4 py-8">
            <!-- Page Title -->
            <h1 class="text-3xl font-bold text-gray-800 mb-8 text-center">Browse Book Categories</h1>

            <!-- Nonfiction Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-semibold text-gray-700 mb-6 pb-2 border-b-2 border-pastel-blue">Nonfiction</h2>
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    <!-- Book 1 -->
                    <div class="book-card bg-white rounded-lg overflow-hidden shadow-md">
                        <div class="book-cover">
                            <img src="src/main/webapp/assets/images/books/book1.png" alt="Sapiens: A Brief History of Humankind">
                        </div>
                        <div class="book-info">
                            <h3 class="font-medium text-gray-800">A Brief History of Humankind</h3>
                            <p class="text-gray-600 text-sm mt-1">Yuval Noah Harari</p>
                            <div class="book-rating">
                                <div class="rating-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                </div>
                                <span class="rating-value">4.5</span>
                            </div>
                            <a href="${pageContext.request.contextPath}/rent-book?book_id=1" class="rent-button" onclick="event.preventDefault(); rentBook(1);">
                                Rent Now
                            </a>
                        </div>
                    </div>

                    <!-- Book 2 -->
                    <div class="book-card bg-white rounded-lg overflow-hidden shadow-md">
                        <div class="book-cover">
                            <img src="assets/images/books/book-2.jpg" alt="To Kill a Mockingbird">
                        </div>
                        <div class="book-info">
                            <h3 class="font-medium text-gray-800">To Kill a Mockingbird</h3>
                            <p class="text-gray-600 text-sm mt-1">Harper Lee</p>
                            <div class="book-rating">
                                <div class="rating-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                </div>
                                <span class="rating-value">4.5</span>
                            </div>ca
                            <a href="${pageContext.request.contextPath}/rent-book?book_id=2" class="rent-button" onclick="event.preventDefault(); rentBook(2);">
                                Rent Now
                            </a>
                        </div>
                    </div>

                    <!-- Book 3 -->
                    <div class="book-card bg-white rounded-lg overflow-hidden shadow-md">
                        <div class="book-cover">
                            <img src="assets/images/books/book-3.jpg" alt="1984">
                        </div>
                        <div class="book-info">
                            <h3 class="font-medium text-gray-800">1984</h3>
                            <p class="text-gray-600 text-sm mt-1">George Orwell</p>
                            <div class="book-rating">
                                <div class="rating-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="rating-value">5.0</span>
                            </div>
                            <a href="${pageContext.request.contextPath}/rent-book?book_id=3" class="rent-button" onclick="event.preventDefault(); rentBook(3);">
                                Rent Now
                            </a>
                        </div>
                    </div>

                    <!-- Book 4 -->
                    <div class="book-card bg-white rounded-lg overflow-hidden shadow-md">
                        <div class="book-cover">
                            <img src="assets/images/books/book-nonfiction-4.jpg" alt="Thinking, Fast and Slow">
                        </div>
                        <div class="book-info">
                            <h3 class="font-medium text-gray-800">Thinking, Fast and Slow</h3>
                            <p class="text-gray-600 text-sm mt-1">Daniel Kahneman</p>
                            <div class="book-rating">
                                <div class="rating-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                </div>
                                <span class="rating-value">4.5</span>
                            </div>
                            <a href="${pageContext.request.contextPath}/rent-book?book_id=4" class="rent-button" onclick="event.preventDefault(); rentBook(4);">
                                Rent Now
                            </a>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Graphic Novels & Comics Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-semibold text-gray-700 mb-6 pb-2 border-b-2 border-pastel-blue">Graphic Novels & Comics</h2>
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    <!-- Book 1 -->
                    <div class="book-card bg-white rounded-lg overflow-hidden shadow-md">
                        <div class="book-cover">
                            <img src="assets/images/books/book-graphic-1.jpg" alt="Watchmen">
                        </div>
                        <div class="book-info">
                            <h3 class="font-medium text-gray-800">Watchmen</h3>
                            <p class="text-gray-600 text-sm mt-1">Alan Moore</p>
                            <div class="book-rating">
                                <div class="rating-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="rating-value">5.0</span>
                            </div>
                            <button class="rent-button">
                                Rent Now
                            </button>
                        </div>
                    </div>

                    <!-- Book 2 -->
                    <div class="book-card bg-white rounded-lg overflow-hidden shadow-md">
                        <div class="book-cover">
                            <img src="assets/images/books/book-graphic-2.jpg" alt="Maus">
                        </div>
                        <div class="book-info">
                            <h3 class="font-medium text-gray-800">Maus</h3>
                            <p class="text-gray-600 text-sm mt-1">Art Spiegelman</p>
                            <div class="book-rating">
                                <div class="rating-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                </div>
                                <span class="rating-value">4.5</span>
                            </div>
                            <a href="${pageContext.request.contextPath}/rent-book?book_id=5" class="rent-button" onclick="event.preventDefault(); rentBook(5);">
                                Rent Now
                            </a>
                        </div>
                    </div>

                    <!-- Book 3 -->
                    <div class="book-card bg-white rounded-lg overflow-hidden shadow-md">
                        <div class="book-cover">
                            <img src="assets/images/books/book-graphic-3.jpg" alt="Persepolis">
                        </div>
                        <div class="book-info">
                            <h3 class="font-medium text-gray-800">Persepolis</h3>
                            <p class="text-gray-600 text-sm mt-1">Marjane Satrapi</p>
                            <div class="book-rating">
                                <div class="rating-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                </div>
                                <span class="rating-value">4.0</span>
                            </div>
                            <button class="rent-button">
                                Rent Now
                            </button>
                        </div>
                    </div>

                    <!-- Book 4 -->
                    <div class="book-card bg-white rounded-lg overflow-hidden shadow-md">
                        <div class="book-cover">
                            <img src="assets/images/books/book-graphic-4.jpg" alt="Saga">
                        </div>
                        <div class="book-info">
                            <h3 class="font-medium text-gray-800">Saga</h3>
                            <p class="text-gray-600 text-sm mt-1">Brian K. Vaughan</p>
                            <div class="book-rating">
                                <div class="rating-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="rating-value">5.0</span>
                            </div>
                            <button class="rent-button">
                                Rent Now
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Fiction Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-semibold text-gray-700 mb-6 pb-2 border-b-2 border-pastel-blue">Fiction</h2>
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    <!-- Book 1 -->
                    <div class="book-card bg-white rounded-lg overflow-hidden shadow-md">
                        <div class="book-cover">
                            <img src="assets/images/books/book-fiction-1.jpg" alt="The Midnight Library">
                        </div>
                        <div class="book-info">
                            <h3 class="font-medium text-gray-800">The Midnight Library</h3>
                            <p class="text-gray-600 text-sm mt-1">Matt Haig</p>
                            <div class="book-rating">
                                <div class="rating-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                </div>
                                <span class="rating-value">4.0</span>
                            </div>
                            <button class="rent-button">
                                Rent Now
                            </button>
                        </div>
                    </div>

                    <!-- Book 2 -->
                    <div class="book-card bg-white rounded-lg overflow-hidden shadow-md">
                        <div class="book-cover">
                            <img src="assets/images/books/book-fiction-2.jpg" alt="It Ends With Us">
                        </div>
                        <div class="book-info">
                            <h3 class="font-medium text-gray-800">It Ends With Us</h3>
                            <p class="text-gray-600 text-sm mt-1">Collen Hoover</p>
                            <div class="book-rating">
                                <div class="rating-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                </div>
                                <span class="rating-value">4.5</span>
                            </div>
                            <button class="rent-button">
                                Rent Now
                            </button>
                        </div>
                    </div>

                    <!-- Book 3 -->
                    <div class="book-card bg-white rounded-lg overflow-hidden shadow-md">
                        <div class="book-cover">
                            <img src="assets/images/books/book-fiction-3.jpg" alt="Klara and the Sun">
                        </div>
                        <div class="book-info">
                            <h3 class="font-medium text-gray-800">Klara and the Sun</h3>
                            <p class="text-gray-600 text-sm mt-1">Kazuo Ishiguro</p>
                            <div class="book-rating">
                                <div class="rating-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                    <i class="far fa-star"></i>
                                </div>
                                <span class="rating-value">3.5</span>
                            </div>
                            <button class="rent-button">
                                Rent Now
                            </button>
                        </div>
                    </div>

                    <!-- Book 4 -->
                    <div class="book-card bg-white rounded-lg overflow-hidden shadow-md">
                        <div class="book-cover">
                            <img src="assets/images/books/book-fiction-4.jpg" alt="Cloud Cuckoo Land">
                        </div>
                        <div class="book-info">
                            <h3 class="font-medium text-gray-800">Cloud Cuckoo Land</h3>
                            <p class="text-gray-600 text-sm mt-1">Anthony Doerr</p>
                            <div class="book-rating">
                                <div class="rating-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                </div>
                                <span class="rating-value">4.0</span>
                            </div>
                            <button class="rent-button">
                                Rent Now
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Memoir & Autobiography Section -->
            <section class="mb-12">
                <h2 class="text-2xl font-semibold text-gray-700 mb-6 pb-2 border-b-2 border-pastel-blue">Memoir & Autobiography</h2>
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    <!-- Book 1 -->
                    <div class="book-card bg-white rounded-lg overflow-hidden shadow-md">
                        <div class="book-cover">
                            <img src="assets/images/books/book-memoir-1.jpg" alt="Becoming">
                        </div>
                        <div class="book-info">
                            <h3 class="font-medium text-gray-800">Becoming</h3>
                            <p class="text-gray-600 text-sm mt-1">Michelle Obama</p>
                            <div class="book-rating">
                                <div class="rating-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="rating-value">5.0</span>
                            </div>
                            <button class="rent-button">
                                Rent Now
                            </button>
                        </div>
                    </div>

                    <!-- Book 2 -->
                    <div class="book-card bg-white rounded-lg overflow-hidden shadow-md">
                        <div class="book-cover">
                            <img src="assets/images/books/book-memoir-2.jpg" alt="Born a Crime">
                        </div>
                        <div class="book-info">
                            <h3 class="font-medium text-gray-800">Born a Crime</h3>
                            <p class="text-gray-600 text-sm mt-1">Trevor Noah</p>
                            <div class="book-rating">
                                <div class="rating-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                </div>
                                <span class="rating-value">4.5</span>
                            </div>
                            <button class="rent-button">
                                Rent Now
                            </button>
                        </div>
                    </div>

                    <!-- Book 3 -->
                    <div class="book-card bg-white rounded-lg overflow-hidden shadow-md">
                        <div class="book-cover">
                            <img src="assets/images/books/book-memoir-3.jpg" alt="The Glass Castle">
                        </div>
                        <div class="book-info">
                            <h3 class="font-medium text-gray-800">The Glass Castle</h3>
                            <p class="text-gray-600 text-sm mt-1">Jeannette Walls</p>
                            <div class="book-rating">
                                <div class="rating-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                </div>
                                <span class="rating-value">4.0</span>
                            </div>
                            <button class="rent-button">
                                Rent Now
                            </button>
                        </div>
                    </div>

                    <!-- Book 4 -->
                    <div class="book-card bg-white rounded-lg overflow-hidden shadow-md">
                        <div class="book-cover">
                            <img src="assets/images/books/book-memoir-4.jpg" alt="I Know Why the Caged Bird Sings">
                        </div>
                        <div class="book-info">
                            <h3 class="font-medium text-gray-800">I Know Why the Caged Bird Sings</h3>
                            <p class="text-gray-600 text-sm mt-1">Maya Angelou</p>
                            <div class="book-rating">
                                <div class="rating-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="rating-value">5.0</span>
                            </div>
                            <button class="rent-button">
                                Rent Now
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <jsp:include page="footer.jsp" />
</body>
</html>