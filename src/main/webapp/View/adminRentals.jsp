<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%@ page import="Model.RentalDetails" %>
<%@ page import="java.util.List" %>
<%@ page import="java.time.format.DateTimeFormatter" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
        response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
        return;
    }

    List<RentalDetails> pendingRentals = (List<RentalDetails>) request.getAttribute("pendingRentals");
    List<RentalDetails> activeRentals = (List<RentalDetails>) request.getAttribute("activeRentals");
    List<RentalDetails> completedRentals = (List<RentalDetails>) request.getAttribute("completedRentals");

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy");
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Rentals - Admin Dashboard - GyanKunja</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/responsive.css">
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --accent: #b388ff;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --input-bg: #f0f4ff;
            --input-border: #e0e7ff;
            --text-main: #222;
            --text-light: #666;
            --success: #4CAF50;
            --warning: #FF9800;
            --danger: #F44336;
            --info: #2196F3;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            max-width: 1200px;
            margin: 10px auto 50px;
            padding: 40px;
            background: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            flex: 1;
        }

        .page-title {
            font-size: 2rem;
            color: var(--primary);
            margin-bottom: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .back-button {
            padding: 0.5rem 1rem;
            background: var(--input-bg);
            color: var(--primary);
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            transition: all 0.3s;
        }

        .back-button:hover {
            background: var(--primary);
            color: var(--white);
        }

        .rentals-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1.5rem;
        }

        .rentals-table th,
        .rentals-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--input-border);
        }

        .rentals-table th {
            background-color: var(--input-bg);
            color: var(--primary);
            font-weight: 600;
        }

        .rentals-table tr:hover {
            background-color: var(--input-bg);
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            display: inline-block;
        }

        .status-pending {
            background-color: rgba(255, 152, 0, 0.1);
            color: var(--warning);
            border: 1px solid var(--warning);
        }

        .action-button {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s;
            display: inline-block;
            margin-right: 0.5rem;
        }

        .btn-approve {
            background-color: var(--success);
            color: var(--white);
        }

        .btn-approve:hover {
            background-color: #3d8b40;
        }

        .btn-reject {
            background-color: var(--danger);
            color: var(--white);
        }

        .btn-reject:hover {
            background-color: #d32f2f;
        }

        .btn-view {
            background-color: var(--info);
            color: var(--white);
        }

        .btn-view:hover {
            background-color: #0b7dda;
        }

        .no-rentals {
            text-align: center;
            padding: 2rem;
            background-color: var(--input-bg);
            border-radius: 8px;
            margin-top: 1.5rem;
        }

        .no-rentals i {
            font-size: 3rem;
            color: var(--text-light);
            margin-bottom: 1rem;
        }

        .no-rentals p {
            color: var(--text-light);
            margin-bottom: 1rem;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: var(--white);
            margin: 10% auto;
            padding: 2rem;
            border-radius: var(--radius);
            max-width: 500px;
            box-shadow: var(--shadow);
        }

        .modal-title {
            color: var(--primary);
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
        }

        .modal-body {
            margin-bottom: 1.5rem;
        }

        .modal-actions {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
        }

        .close {
            color: var(--text-light);
            float: right;
            font-size: 1.5rem;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: var(--text-main);
        }
    </style>
</head>
<body>
    <jsp:include page="adminHeader.jsp" />

    <div class="main-content">
        <div class="page-title">
            <h1>Manage Rental Requests</h1>
            <a href="${pageContext.request.contextPath}/View/AdminDashboard.jsp" class="back-button">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>

        <!-- Tabs for different rental statuses -->
        <div class="rental-tabs" style="display: flex; gap: 10px; margin-bottom: 20px;">
            <button class="tab-button active" onclick="openTab(event, 'pending')" style="padding: 10px 20px; background-color: var(--primary); color: white; border: none; border-radius: 5px; cursor: pointer;">Pending Rentals</button>
            <button class="tab-button" onclick="openTab(event, 'active')" style="padding: 10px 20px; background-color: var(--input-bg); color: var(--text-main); border: none; border-radius: 5px; cursor: pointer;">Active Rentals</button>
            <button class="tab-button" onclick="openTab(event, 'completed')" style="padding: 10px 20px; background-color: var(--input-bg); color: var(--text-main); border: none; border-radius: 5px; cursor: pointer;">Completed Rentals</button>
        </div>

        <!-- Pending Rentals Tab -->
        <div id="pending" class="tab-content" style="display: block;">
            <h2 class="tab-title" style="margin-bottom: 15px; color: var(--primary);">Pending Rental Requests</h2>
            <% if (pendingRentals != null && !pendingRentals.isEmpty()) { %>
                <table class="rentals-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Book</th>
                            <th>Rental Date</th>
                            <th>Return Date</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% for (RentalDetails rental : pendingRentals) { %>
                            <tr>
                                <td><%= rental.getRentalDetailId() %></td>
                                <td><%= rental.getUserName() != null ? rental.getUserName() : "User #" + rental.getUserId() %></td>
                                <td><%= rental.getBookTitle() != null ? rental.getBookTitle() : "Book #" + rental.getBookId() %></td>
                                <td><%= rental.getRentalDate().format(formatter) %></td>
                                <td><%= rental.getReturnDate().format(formatter) %></td>
                                <td>Rs. <%= rental.getRentalAmount() %></td>
                                <td>
                                    <span class="status-badge status-pending">Pending</span>
                                </td>
                                <td>
                                    <button onclick="approveRental(<%= rental.getRentalDetailId() %>)" class="action-button btn-approve">
                                        <i class="fas fa-check"></i> Approve
                                    </button>
                                    <button onclick="rejectRental(<%= rental.getRentalDetailId() %>)" class="action-button btn-reject">
                                        <i class="fas fa-times"></i> Reject
                                    </button>
                                    <a href="${pageContext.request.contextPath}/view-rental/<%= rental.getRentalDetailId() %>" class="action-button btn-view">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        <% } %>
                    </tbody>
                </table>
            <% } else { %>
                <div class="no-rentals">
                    <i class="fas fa-check-circle"></i>
                    <h3>No Pending Rental Requests</h3>
                    <p>There are no rental requests waiting for approval.</p>
                </div>
            <% } %>
        </div>

        <!-- Active Rentals Tab -->
        <div id="active" class="tab-content" style="display: none;">
            <h2 class="tab-title" style="margin-bottom: 15px; color: var(--primary);">Active Rentals</h2>
            <% if (activeRentals != null && !activeRentals.isEmpty()) { %>
                <table class="rentals-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Book</th>
                            <th>Rental Date</th>
                            <th>Return Date</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% for (RentalDetails rental : activeRentals) { %>
                            <tr>
                                <td><%= rental.getRentalDetailId() %></td>
                                <td><%= rental.getUserName() != null ? rental.getUserName() : "User #" + rental.getUserId() %></td>
                                <td><%= rental.getBookTitle() != null ? rental.getBookTitle() : "Book #" + rental.getBookId() %></td>
                                <td><%= rental.getRentalDate().format(formatter) %></td>
                                <td><%= rental.getReturnDate().format(formatter) %></td>
                                <td>Rs. <%= rental.getRentalAmount() %></td>
                                <td>
                                    <span class="status-badge status-active">Active</span>
                                </td>
                                <td>
                                    <button onclick="completeRental(<%= rental.getRentalDetailId() %>)" class="action-button btn-approve">
                                        <i class="fas fa-check-circle"></i> Complete
                                    </button>
                                    <a href="${pageContext.request.contextPath}/view-rental/<%= rental.getRentalDetailId() %>" class="action-button btn-view">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        <% } %>
                    </tbody>
                </table>
            <% } else { %>
                <div class="no-rentals">
                    <i class="fas fa-info-circle"></i>
                    <h3>No Active Rentals</h3>
                    <p>There are no active rentals at the moment.</p>
                </div>
            <% } %>
        </div>

        <!-- Completed Rentals Tab -->
        <div id="completed" class="tab-content" style="display: none;">
            <h2 class="tab-title" style="margin-bottom: 15px; color: var(--primary);">Completed Rentals</h2>
            <% if (completedRentals != null && !completedRentals.isEmpty()) { %>
                <table class="rentals-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Book</th>
                            <th>Rental Date</th>
                            <th>Return Date</th>
                            <th>Actual Return</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% for (RentalDetails rental : completedRentals) { %>
                            <tr>
                                <td><%= rental.getRentalDetailId() %></td>
                                <td><%= rental.getUserName() != null ? rental.getUserName() : "User #" + rental.getUserId() %></td>
                                <td><%= rental.getBookTitle() != null ? rental.getBookTitle() : "Book #" + rental.getBookId() %></td>
                                <td><%= rental.getRentalDate().format(formatter) %></td>
                                <td><%= rental.getReturnDate().format(formatter) %></td>
                                <td><%= rental.getActualReturnDate() != null ? rental.getActualReturnDate().format(formatter) : "N/A" %></td>
                                <td>Rs. <%= rental.getRentalAmount() %></td>
                                <td>
                                    <span class="status-badge status-completed">Completed</span>
                                </td>
                                <td>
                                    <a href="${pageContext.request.contextPath}/view-rental/<%= rental.getRentalDetailId() %>" class="action-button btn-view">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        <% } %>
                    </tbody>
                </table>
            <% } else { %>
                <div class="no-rentals">
                    <i class="fas fa-info-circle"></i>
                    <h3>No Completed Rentals</h3>
                    <p>There are no completed rentals at the moment.</p>
                </div>
            <% } %>
        </div>
    </div>

    <!-- Approve Rental Modal -->
    <div id="approveModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('approveModal')">&times;</span>
            <h2 class="modal-title">Approve Rental Request</h2>
            <div class="modal-body">
                <p>Are you sure you want to approve this rental request?</p>
                <p>This will mark the rental as active and confirm the book reservation.</p>
            </div>
            <div class="modal-actions">
                <button onclick="closeModal('approveModal')" class="action-button btn-secondary">Cancel</button>
                <form id="approveForm" action="${pageContext.request.contextPath}/admin/rental-approval" method="post" style="display: inline;">
                    <input type="hidden" id="approveRentalId" name="rental_detail_id" value="">
                    <input type="hidden" name="action" value="approve">
                    <button type="submit" class="action-button btn-approve">Confirm Approval</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Reject Rental Modal -->
    <div id="rejectModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('rejectModal')">&times;</span>
            <h2 class="modal-title">Reject Rental Request</h2>
            <div class="modal-body">
                <p>Are you sure you want to reject this rental request?</p>
                <p>This will cancel the rental and make the book available for others.</p>
            </div>
            <div class="modal-actions">
                <button onclick="closeModal('rejectModal')" class="action-button btn-secondary">Cancel</button>
                <form id="rejectForm" action="${pageContext.request.contextPath}/admin/rental-approval" method="post" style="display: inline;">
                    <input type="hidden" id="rejectRentalId" name="rental_detail_id" value="">
                    <input type="hidden" name="action" value="reject">
                    <button type="submit" class="action-button btn-reject">Confirm Rejection</button>
                </form>
            </div>
        </div>
    </div>

    <jsp:include page="footer.jsp" />

    <script>
        // Tab switching function
        function openTab(evt, tabName) {
            // Hide all tab contents
            var tabContents = document.getElementsByClassName("tab-content");
            for (var i = 0; i < tabContents.length; i++) {
                tabContents[i].style.display = "none";
            }

            // Remove active class from all tab buttons
            var tabButtons = document.getElementsByClassName("tab-button");
            for (var i = 0; i < tabButtons.length; i++) {
                tabButtons[i].className = tabButtons[i].className.replace(" active", "");
                tabButtons[i].style.backgroundColor = "var(--input-bg)";
                tabButtons[i].style.color = "var(--text-main)";
            }

            // Show the current tab and add active class to the button
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
            evt.currentTarget.style.backgroundColor = "var(--primary)";
            evt.currentTarget.style.color = "white";
        }

        // Complete rental function
        function completeRental(rentalId) {
            if (confirm("Are you sure you want to mark this rental as completed?")) {
                // Create a form and submit it
                var form = document.createElement("form");
                form.method = "POST";
                form.action = "${pageContext.request.contextPath}/admin/rental-approval";

                var actionInput = document.createElement("input");
                actionInput.type = "hidden";
                actionInput.name = "action";
                actionInput.value = "complete";
                form.appendChild(actionInput);

                var rentalIdInput = document.createElement("input");
                rentalIdInput.type = "hidden";
                rentalIdInput.name = "rentalId";
                rentalIdInput.value = rentalId;
                form.appendChild(rentalIdInput);

                document.body.appendChild(form);
                form.submit();
            }
        }

        function approveRental(rentalId) {
            document.getElementById('approveRentalId').value = rentalId;
            document.getElementById('approveModal').style.display = 'block';
        }

        function rejectRental(rentalId) {
            document.getElementById('rejectRentalId').value = rentalId;
            document.getElementById('rejectModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            if (event.target.className === 'modal') {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html>
