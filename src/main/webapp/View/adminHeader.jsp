<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
        response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
        return;
    }
    String currentPage = request.getRequestURI();
%>
<!-- Admin Navbar -->
<header class="admin-header-container">
    <div class="admin-header-top">
        <div class="logo-section">
            <a href="${pageContext.request.contextPath}/View/AdminDashboard.jsp" class="logo-link">
                <img src="${pageContext.request.contextPath}/assets/images/books/Gyankunj.png" alt="GyanKunja Logo" class="logo-img">
                <h1 class="website-name">GyanKunja Admin</h1>
            </a>
        </div>
        <div class="admin-user-section">
            <span class="admin-welcome">Welcome, <strong><%= user.getUserName() %></strong></span>
            <a href="${pageContext.request.contextPath}/ForLogOut" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
        </div>
    </div>
    <button class="mobile-menu-toggle" id="mobileMenuToggle">
        <i class="fas fa-bars"></i>
    </button>

    <nav class="admin-nav" id="adminNav">
        <a href="${pageContext.request.contextPath}/View/AdminDashboard.jsp" class="admin-nav-link <%= currentPage.endsWith("/AdminDashboard.jsp") ? "active" : "" %>">
            <i class="fas fa-tachometer-alt"></i> <span class="nav-text">Dashboard</span>
        </a>
        <a href="${pageContext.request.contextPath}/admin/users" class="admin-nav-link <%= currentPage.contains("/admin/users") || currentPage.endsWith("/adminUsers.jsp") ? "active" : "" %>">
            <i class="fas fa-users"></i> <span class="nav-text">Users</span>
        </a>
        <a href="${pageContext.request.contextPath}/admin/books" class="admin-nav-link <%= currentPage.contains("/admin/books") || currentPage.endsWith("/adminBooks.jsp") ? "active" : "" %>">
            <i class="fas fa-book"></i> <span class="nav-text">Books</span>
        </a>
        <a href="${pageContext.request.contextPath}/admin/rentals" class="admin-nav-link <%= currentPage.contains("/admin/rentals") || currentPage.endsWith("/adminRentals.jsp") ? "active" : "" %>">
            <i class="fas fa-exchange-alt"></i> <span class="nav-text">Rentals</span>
        </a>
        <a href="${pageContext.request.contextPath}/admin/reports" class="admin-nav-link <%= currentPage.contains("/admin/reports") || currentPage.endsWith("/adminReports.jsp") ? "active" : "" %>">
            <i class="fas fa-chart-bar"></i> <span class="nav-text">Reports</span>
        </a>
    </nav>
</header>

<style>
    .admin-header-container {
        background-color: #fff;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        position: relative;
        z-index: 1000;
    }

    .admin-header-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
    }

    .logo-link {
        display: flex;
        align-items: center;
        gap: 12px;
        text-decoration: none;
        transition: transform 0.2s ease;
    }

    .logo-link:hover {
        transform: translateY(-1px);
    }

    .logo-img {
        height: 40px;
        width: auto;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .website-name {
        color: #5b6dfa;
        font-size: 1.3rem;
        margin: 0;
        font-weight: 600;
        letter-spacing: 0.5px;
    }

    .admin-user-section {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .admin-welcome {
        font-weight: 500;
        color: #333;
        font-size: 0.95rem;
        display: flex;
        align-items: center;
    }

    .admin-welcome strong {
        color: #5b6dfa;
        margin-left: 4px;
    }

    .logout-btn {
        background-color: #5b6dfa;
        color: white;
        padding: 8px 15px;
        border-radius: 4px;
        text-decoration: none;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 5px;
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(91, 109, 250, 0.2);
    }

    .logout-btn:hover {
        background-color: #4a5bd9;
        transform: translateY(-1px);
        box-shadow: 0 4px 6px rgba(91, 109, 250, 0.3);
    }

    .admin-nav {
        display: flex;
        padding: 0 20px;
        overflow-x: auto;
        background-color: #f8f9ff;
        border-bottom: 1px solid #eaedf8;
    }

    .admin-nav-link {
        padding: 15px 20px;
        color: #555;
        text-decoration: none;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
        border-bottom: 3px solid transparent;
        transition: all 0.2s ease;
        position: relative;
    }

    .admin-nav-link i {
        font-size: 1.1rem;
        color: #6b7280;
    }

    .admin-nav-link:hover {
        color: #5b6dfa;
        border-bottom-color: #5b6dfa;
        background-color: rgba(91, 109, 250, 0.05);
    }

    .admin-nav-link:hover i {
        color: #5b6dfa;
    }

    .admin-nav-link.active {
        color: #5b6dfa;
        border-bottom-color: #5b6dfa;
        background-color: rgba(91, 109, 250, 0.08);
        font-weight: 600;
    }

    .admin-nav-link.active i {
        color: #5b6dfa;
    }

    .mobile-menu-toggle {
        display: none;
        background: #5b6dfa;
        border: none;
        color: white;
        font-size: 1.2rem;
        cursor: pointer;
        padding: 8px 12px;
        position: absolute;
        right: 20px;
        top: 15px;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(91, 109, 250, 0.2);
        transition: all 0.2s ease;
    }

    .mobile-menu-toggle:hover {
        background: #4a5bd9;
        transform: translateY(-1px);
    }

    @media (max-width: 992px) {
        .admin-header-top {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            padding-right: 60px; /* Space for mobile toggle */
        }

        .admin-user-section {
            width: 100%;
            justify-content: flex-end;
            gap: 15px;
        }

        .mobile-menu-toggle {
            display: block;
        }

        .admin-nav {
            display: none;
            flex-direction: column;
            width: 100%;
            padding: 0;
        }

        .admin-nav.active {
            display: flex;
        }

        .admin-nav-link {
            padding: 15px 20px;
            font-size: 1rem;
            border-bottom: 1px solid #eee;
            border-left: 3px solid transparent;
            background-color: #fff;
        }

        .admin-nav-link.active {
            border-bottom: 1px solid #eee;
            border-left: 3px solid #5b6dfa;
            background-color: #f8f9ff;
        }

        .admin-nav-link:hover {
            background-color: #f8f9ff;
            border-bottom: 1px solid #eee;
        }
    }

    @media (max-width: 576px) {
        .admin-welcome {
            display: none;
        }

        .logo-img {
            height: 30px;
        }

        .website-name {
            font-size: 1.1rem;
        }

        .admin-header-top {
            padding: 12px 15px;
        }

        .admin-nav-link {
            padding: 12px 15px;
        }

        .admin-nav-link .nav-text {
            font-size: 0.9rem;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const adminNav = document.getElementById('adminNav');

        if (mobileMenuToggle && adminNav) {
            mobileMenuToggle.addEventListener('click', function() {
                adminNav.classList.toggle('active');
            });
        }
    });
</script>