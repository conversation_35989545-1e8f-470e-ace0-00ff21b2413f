<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
        response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
        return;
    }
    String currentPage = request.getRequestURI();
%>
<!-- Admin Navbar -->
<header class="admin-header-container">
    <div class="admin-header-top">
        <div class="logo-section">
            <a href="${pageContext.request.contextPath}/View/AdminDashboard.jsp" class="logo-link">
                <img src="${pageContext.request.contextPath}/assets/images/books/Gyankunj.png" alt="GyanKunja Logo" class="logo-img" style="height: 40px; width: auto;">
                <h1 class="website-name">GyanKunja Admin</h1>
            </a>
        </div>
        <div class="admin-user-section">
            <span class="admin-welcome">Welcome, <%= user.getUserName() %></span>
            <a href="${pageContext.request.contextPath}/ForLogOut" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
        </div>
    </div>
    <button class="mobile-menu-toggle" id="mobileMenuToggle">
        <i class="fas fa-bars"></i>
    </button>

    <nav class="admin-nav" id="adminNav">
        <a href="${pageContext.request.contextPath}/View/AdminDashboard.jsp" class="admin-nav-link <%= currentPage.endsWith("/AdminDashboard.jsp") ? "active" : "" %>">
            <i class="fas fa-tachometer-alt"></i> <span class="nav-text">Dashboard</span>
        </a>
        <a href="${pageContext.request.contextPath}/admin/users" class="admin-nav-link <%= currentPage.contains("/admin/users") || currentPage.endsWith("/adminUsers.jsp") ? "active" : "" %>">
            <i class="fas fa-users"></i> <span class="nav-text">Users</span>
        </a>
        <a href="${pageContext.request.contextPath}/admin/books" class="admin-nav-link <%= currentPage.contains("/admin/books") || currentPage.endsWith("/adminBooks.jsp") ? "active" : "" %>">
            <i class="fas fa-book"></i> <span class="nav-text">Books</span>
        </a>
        <a href="${pageContext.request.contextPath}/admin/rentals" class="admin-nav-link <%= currentPage.contains("/admin/rentals") || currentPage.endsWith("/adminRentals.jsp") ? "active" : "" %>">
            <i class="fas fa-exchange-alt"></i> <span class="nav-text">Rentals</span>
        </a>
        <a href="${pageContext.request.contextPath}/admin/reports" class="admin-nav-link <%= currentPage.contains("/admin/reports") || currentPage.endsWith("/adminReports.jsp") ? "active" : "" %>">
            <i class="fas fa-chart-bar"></i> <span class="nav-text">Reports</span>
        </a>
    </nav>
</header>

<style>
    .admin-header-container {
        background-color: #fff;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }

    .admin-header-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
    }

    .logo-link {
        display: flex;
        align-items: center;
        gap: 10px;
        text-decoration: none;
    }

    .logo-img {
        height: 30px;
    }

    .website-name {
        color: #5b6dfa;
        font-size: 1.2rem;
        margin: 0;
    }

    .admin-user-section {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .admin-welcome {
        font-weight: 500;
        color: #333;
    }

    .logout-btn {
        background-color: #5b6dfa;
        color: white;
        padding: 6px 12px;
        border-radius: 4px;
        text-decoration: none;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .admin-nav {
        display: flex;
        padding: 0 20px;
        overflow-x: auto;
    }

    .admin-nav-link {
        padding: 15px;
        color: #555;
        text-decoration: none;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 5px;
        border-bottom: 3px solid transparent;
        transition: all 0.2s ease;
    }

    .admin-nav-link:hover {
        color: #5b6dfa;
        border-bottom-color: #5b6dfa;
    }

    .admin-nav-link.active {
        color: #5b6dfa;
        border-bottom-color: #5b6dfa;
    }

    .mobile-menu-toggle {
        display: none;
        background: none;
        border: none;
        color: #5b6dfa;
        font-size: 1.5rem;
        cursor: pointer;
        padding: 10px;
        position: absolute;
        right: 20px;
        top: 15px;
    }

    @media (max-width: 992px) {
        .admin-header-top {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            padding-right: 60px; /* Space for mobile toggle */
        }

        .admin-user-section {
            width: 100%;
            justify-content: space-between;
        }

        .mobile-menu-toggle {
            display: block;
        }

        .admin-nav {
            display: none;
            flex-direction: column;
            width: 100%;
            padding: 0;
        }

        .admin-nav.active {
            display: flex;
        }

        .admin-nav-link {
            padding: 12px 20px;
            font-size: 1rem;
            border-bottom: 1px solid #eee;
            border-left: 3px solid transparent;
        }

        .admin-nav-link.active {
            border-bottom: 1px solid #eee;
            border-left: 3px solid #5b6dfa;
        }
    }

    @media (max-width: 576px) {
        .admin-welcome {
            display: none;
        }

        .logo-img {
            height: 25px;
        }

        .website-name {
            font-size: 1rem;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const adminNav = document.getElementById('adminNav');

        if (mobileMenuToggle && adminNav) {
            mobileMenuToggle.addEventListener('click', function() {
                adminNav.classList.toggle('active');
            });
        }
    });
</script>