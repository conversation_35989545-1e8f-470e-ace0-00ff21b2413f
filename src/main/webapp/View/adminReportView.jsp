<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%@ page import="java.util.Map" %>
<%@ page import="java.util.List" %>
<%@ page import="java.time.LocalDate" %>
<%@ page import="java.time.format.DateTimeFormatter" %>
<%@ page import="com.google.gson.Gson" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
        response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
        return;
    }

    String reportType = (String) request.getAttribute("reportType");
    LocalDate startDate = (LocalDate) request.getAttribute("startDate");
    LocalDate endDate = (LocalDate) request.getAttribute("endDate");

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy");
    Gson gson = new Gson();
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= reportType %> Report - GyanKunja</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .report-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .report-header h2 {
            margin: 0;
            color: #5b6dfa;
            font-size: 1.5rem;
        }

        .report-meta {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .report-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background-color: #5b6dfa;
            color: white;
            border: none;
        }

        .btn-secondary {
            background-color: #f0f0f0;
            color: #333;
            border: 1px solid #ddd;
        }

        .report-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .chart-container {
            width: 100%;
            height: 300px;
            margin-bottom: 20px;
        }

        .report-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .report-table th, .report-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .report-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .report-table tr:hover {
            background-color: #f9f9f9;
        }

        .tab-container {
            margin-top: 20px;
        }

        .tab-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .tab-button {
            padding: 8px 16px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }

        .tab-button.active {
            background-color: #5b6dfa;
            color: white;
            border-color: #5b6dfa;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Include header and navigation -->
        <jsp:include page="adminHeader.jsp" />

        <div class="main-content">
            <div class="report-container">
                <div class="report-header">
                    <div>
                        <h2><%= reportType %> Report</h2>
                        <div class="report-meta">
                            Period: <%= startDate.format(formatter) %> to <%= endDate.format(formatter) %>
                        </div>
                    </div>
                    <div class="report-actions">
                        <button onclick="window.print()" class="btn btn-primary"><i class="fas fa-print"></i> Print</button>
                        <a href="${pageContext.request.contextPath}/admin/reports" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to Reports</a>
                    </div>
                </div>

                <% if (reportType.equals("Rental Activity")) { %>
                    <% Map<String, Object> report = (Map<String, Object>) request.getAttribute("report"); %>

                    <div class="tab-container">
                        <div class="tab-buttons">
                            <button class="tab-button active" onclick="showTab('chart')">Chart View</button>
                            <button class="tab-button" onclick="showTab('table')">Table View</button>
                        </div>

                        <div id="chart" class="tab-content active">
                            <div class="chart-container">
                                <canvas id="rentalChart"></canvas>
                            </div>
                        </div>

                        <div id="table" class="tab-content">
                            <table class="report-table">
                                <tr>
                                    <th>Total Rentals</th>
                                    <th>Active Rentals</th>
                                    <th>Completed Rentals</th>
                                    <th>Pending Rentals</th>
                                    <th>Total Revenue</th>
                                </tr>
                                <tr>
                                    <td><%= report.get("totalRentals") %></td>
                                    <td><%= report.get("activeRentals") %></td>
                                    <td><%= report.get("completedRentals") %></td>
                                    <td><%= report.get("pendingRentals") %></td>
                                    <td>₹<%= String.format("%.2f", report.get("totalRevenue")) %></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <script>
                        // Prepare data for chart
                        const rentalData = {
                            labels: ['Active Rentals', 'Completed Rentals', 'Pending Rentals'],
                            datasets: [{
                                label: 'Rental Status',
                                data: [<%= report.get("activeRentals") %>, <%= report.get("completedRentals") %>, <%= report.get("pendingRentals") %>],
                                backgroundColor: ['#36A2EB', '#4BC0C0', '#FFCD56'],
                                hoverOffset: 4
                            }]
                        };

                        // Create chart
                        const rentalCtx = document.getElementById('rentalChart').getContext('2d');
                        const rentalChart = new Chart(rentalCtx, {
                            type: 'pie',
                            data: rentalData,
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    title: {
                                        display: true,
                                        text: 'Rental Status Distribution',
                                        font: { size: 16 }
                                    },
                                    legend: {
                                        position: 'bottom'
                                    }
                                }
                            }
                        });
                    </script>
                <% } else if (reportType.equals("Popular Books")) { %>
                    <% List<Map<String, Object>> report = (List<Map<String, Object>>) request.getAttribute("report"); %>

                    <div class="tab-container">
                        <div class="tab-buttons">
                            <button class="tab-button active" onclick="showTab('chart')">Chart View</button>
                            <button class="tab-button" onclick="showTab('table')">Table View</button>
                        </div>

                        <div id="chart" class="tab-content active">
                            <div class="chart-container">
                                <canvas id="booksChart"></canvas>
                            </div>
                        </div>

                        <div id="table" class="tab-content">
                            <table class="report-table">
                                <thead>
                                    <tr>
                                        <th>Rank</th>
                                        <th>Book Title</th>
                                        <th>Author</th>
                                        <th>Rental Count</th>
                                        <th>Revenue</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% if (report != null && !report.isEmpty()) { %>
                                        <% int rank = 1; %>
                                        <% for (Map<String, Object> book : report) { %>
                                            <tr>
                                                <td><%= rank++ %></td>
                                                <td><%= book.get("title") %></td>
                                                <td><%= book.get("author") %></td>
                                                <td><%= book.get("rentalCount") %></td>
                                                <td>₹<%= String.format("%.2f", book.get("revenue")) %></td>
                                            </tr>
                                        <% } %>
                                    <% } else { %>
                                        <tr>
                                            <td colspan="5" style="text-align: center;">No data available for the selected period.</td>
                                        </tr>
                                    <% } %>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <% if (report != null && !report.isEmpty()) { %>
                    <script>
                        // Prepare data for chart
                        const bookLabels = [];
                        const bookData = [];
                        const bookColors = [
                            '#FF6384', '#36A2EB', '#FFCD56', '#4BC0C0', '#9966FF',
                            '#FF9F40', '#C9CBCF', '#7FC8F8', '#FFABAB', '#A5D6A7'
                        ];

                        <% int count = 0; %>
                        <% for (Map<String, Object> book : report) { %>
                            <% if (count < 5) { %> // Limit to top 5 for better visualization
                                bookLabels.push('<%= book.get("title") %>');
                                bookData.push(<%= book.get("rentalCount") %>);
                                <% count++; %>
                            <% } %>
                        <% } %>

                        // Create chart
                        const booksCtx = document.getElementById('booksChart').getContext('2d');
                        const booksChart = new Chart(booksCtx, {
                            type: 'bar',
                            data: {
                                labels: bookLabels,
                                datasets: [{
                                    label: 'Rental Count',
                                    data: bookData,
                                    backgroundColor: bookColors,
                                    borderWidth: 1
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    title: {
                                        display: true,
                                        text: 'Top 5 Most Popular Books',
                                        font: { size: 16 }
                                    },
                                    legend: {
                                        display: false
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        title: {
                                            display: true,
                                            text: 'Number of Rentals'
                                        }
                                    },
                                    x: {
                                        ticks: {
                                            maxRotation: 45,
                                            minRotation: 45
                                        }
                                    }
                                }
                            }
                        });
                    </script>
                    <% } %>
                <% } else if (reportType.equals("User Activity")) { %>
                    <% List<Map<String, Object>> report = (List<Map<String, Object>>) request.getAttribute("report"); %>

                    <div class="tab-container">
                        <div class="tab-buttons">
                            <button class="tab-button active" onclick="showTab('chart')">Chart View</button>
                            <button class="tab-button" onclick="showTab('table')">Table View</button>
                        </div>

                        <div id="chart" class="tab-content active">
                            <div class="chart-container">
                                <canvas id="usersChart"></canvas>
                            </div>
                        </div>

                        <div id="table" class="tab-content">
                            <table class="report-table">
                                <thead>
                                    <tr>
                                        <th>Rank</th>
                                        <th>Username</th>
                                        <th>Email</th>
                                        <th>Rental Count</th>
                                        <th>Total Spent</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% if (report != null && !report.isEmpty()) { %>
                                        <% int rank = 1; %>
                                        <% for (Map<String, Object> user : report) { %>
                                            <tr>
                                                <td><%= rank++ %></td>
                                                <td><%= user.get("username") %></td>
                                                <td><%= user.get("email") %></td>
                                                <td><%= user.get("rentalCount") %></td>
                                                <td>₹<%= String.format("%.2f", user.get("totalSpent")) %></td>
                                            </tr>
                                        <% } %>
                                    <% } else { %>
                                        <tr>
                                            <td colspan="5" style="text-align: center;">No data available for the selected period.</td>
                                        </tr>
                                    <% } %>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <% if (report != null && !report.isEmpty()) { %>
                    <script>
                        // Prepare data for chart
                        const userLabels = [];
                        const userData = [];
                        const spentData = [];

                        <% int count = 0; %>
                        <% for (Map<String, Object> user : report) { %>
                            <% if (count < 5) { %> // Limit to top 5 for better visualization
                                userLabels.push('<%= user.get("username") %>');
                                userData.push(<%= user.get("rentalCount") %>);
                                spentData.push(<%= user.get("totalSpent") %>);
                                <% count++; %>
                            <% } %>
                        <% } %>

                        // Create chart
                        const usersCtx = document.getElementById('usersChart').getContext('2d');
                        const usersChart = new Chart(usersCtx, {
                            type: 'bar',
                            data: {
                                labels: userLabels,
                                datasets: [{
                                    label: 'Rental Count',
                                    data: userData,
                                    backgroundColor: '#36A2EB',
                                    borderWidth: 1,
                                    yAxisID: 'y'
                                }, {
                                    label: 'Total Spent (₹)',
                                    data: spentData,
                                    backgroundColor: '#FF6384',
                                    borderWidth: 1,
                                    yAxisID: 'y1',
                                    type: 'line',
                                    fill: false,
                                    tension: 0.4
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    title: {
                                        display: true,
                                        text: 'Top 5 Most Active Users',
                                        font: { size: 16 }
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        position: 'left',
                                        title: {
                                            display: true,
                                            text: 'Number of Rentals'
                                        }
                                    },
                                    y1: {
                                        beginAtZero: true,
                                        position: 'right',
                                        grid: {
                                            drawOnChartArea: false
                                        },
                                        title: {
                                            display: true,
                                            text: 'Amount Spent (₹)'
                                        }
                                    }
                                }
                            }
                        });
                    </script>
                    <% } %>
                <% } %>
            </div>
        </div>

        <!-- Include footer -->
        <jsp:include page="footer.jsp" />
    </div>

    <script>
        // Function to switch between tabs
        function showTab(tabId) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Deactivate all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Show the selected tab content
            document.getElementById(tabId).classList.add('active');

            // Activate the clicked button
            event.target.classList.add('active');
        }
    </script>
</body>
</html>