<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%@ page import="java.util.Map" %>
<%@ page import="java.util.List" %>
<%@ page import="java.time.LocalDate" %>
<%@ page import="java.time.format.DateTimeFormatter" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
        response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
        return;
    }

    String reportType = (String) request.getAttribute("reportType");
    LocalDate startDate = (LocalDate) request.getAttribute("startDate");
    LocalDate endDate = (LocalDate) request.getAttribute("endDate");

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy");
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= reportType %> Report - GyanKunja</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        .report-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .report-header h2 {
            margin: 0;
            color: #5b6dfa;
            font-size: 1.5rem;
        }

        .report-meta {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .report-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background-color: #5b6dfa;
            color: white;
            border: none;
        }

        .btn-secondary {
            background-color: #f0f0f0;
            color: #333;
            border: 1px solid #ddd;
        }

        .report-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .report-table th, .report-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .report-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .report-table tr:hover {
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Include header and navigation -->
        <jsp:include page="adminHeader.jsp" />

        <div class="main-content">
            <div class="report-container">
                <div class="report-header">
                    <div>
                        <h2><%= reportType %> Report</h2>
                        <div class="report-meta">
                            Period: <%= startDate.format(formatter) %> to <%= endDate.format(formatter) %>
                        </div>
                    </div>
                    <div class="report-actions">
                        <button onclick="window.print()" class="btn btn-primary"><i class="fas fa-print"></i> Print</button>
                        <a href="${pageContext.request.contextPath}/admin/reports" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to Reports</a>
                    </div>
                </div>

                <% if (reportType.equals("Rental Activity")) { %>
                    <% Map<String, Object> report = (Map<String, Object>) request.getAttribute("report"); %>
                    <table class="report-table">
                        <tr>
                            <th>Total Rentals</th>
                            <th>Active Rentals</th>
                            <th>Completed Rentals</th>
                            <th>Pending Rentals</th>
                            <th>Total Revenue</th>
                        </tr>
                        <tr>
                            <td><%= report.get("totalRentals") %></td>
                            <td><%= report.get("activeRentals") %></td>
                            <td><%= report.get("completedRentals") %></td>
                            <td><%= report.get("pendingRentals") %></td>
                            <td>₹<%= String.format("%.2f", report.get("totalRevenue")) %></td>
                        </tr>
                    </table>
                <% } else if (reportType.equals("Popular Books")) { %>
                    <% List<Map<String, Object>> report = (List<Map<String, Object>>) request.getAttribute("report"); %>
                    <table class="report-table">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Book Title</th>
                                <th>Author</th>
                                <th>Rental Count</th>
                                <th>Revenue</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% if (report != null && !report.isEmpty()) { %>
                                <% int rank = 1; %>
                                <% for (Map<String, Object> book : report) { %>
                                    <tr>
                                        <td><%= rank++ %></td>
                                        <td><%= book.get("title") %></td>
                                        <td><%= book.get("author") %></td>
                                        <td><%= book.get("rentalCount") %></td>
                                        <td>₹<%= String.format("%.2f", book.get("revenue")) %></td>
                                    </tr>
                                <% } %>
                            <% } else { %>
                                <tr>
                                    <td colspan="5" style="text-align: center;">No data available for the selected period.</td>
                                </tr>
                            <% } %>
                        </tbody>
                    </table>
                <% } else if (reportType.equals("User Activity")) { %>
                    <% List<Map<String, Object>> report = (List<Map<String, Object>>) request.getAttribute("report"); %>
                    <table class="report-table">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Rental Count</th>
                                <th>Total Spent</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% if (report != null && !report.isEmpty()) { %>
                                <% int rank = 1; %>
                                <% for (Map<String, Object> user : report) { %>
                                    <tr>
                                        <td><%= rank++ %></td>
                                        <td><%= user.get("username") %></td>
                                        <td><%= user.get("email") %></td>
                                        <td><%= user.get("rentalCount") %></td>
                                        <td>₹<%= String.format("%.2f", user.get("totalSpent")) %></td>
                                    </tr>
                                <% } %>
                            <% } else { %>
                                <tr>
                                    <td colspan="5" style="text-align: center;">No data available for the selected period.</td>
                                </tr>
                            <% } %>
                        </tbody>
                    </table>
                <% } %>
            </div>
        </div>

        <!-- Include footer -->
        <jsp:include page="footer.jsp" />
    </div>
</body>
</html>