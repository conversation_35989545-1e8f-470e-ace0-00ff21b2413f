<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%@ page import="java.util.Map" %>
<%@ page import="java.util.List" %>
<%@ page import="java.time.LocalDate" %>
<%@ page import="java.time.format.DateTimeFormatter" %>
<%@ page import="com.google.gson.Gson" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
        response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
        return;
    }

    String reportType = (String) request.getAttribute("reportType");
    LocalDate startDate = (LocalDate) request.getAttribute("startDate");
    LocalDate endDate = (LocalDate) request.getAttribute("endDate");

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy");
    Gson gson = new Gson();
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= reportType %> Report - GyanKunja Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --accent: #b388ff;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --text-main: #222;
            --text-light: #666;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
            padding-top: 60px; /* Space for fixed header */
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .main-content {
            padding: 20px 0;
        }

        .report-section {
            background: var(--white);
            border-radius: var(--radius);
            padding: 20px;
            box-shadow: var(--shadow);
            margin-bottom: 30px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .page-title {
            font-size: 1.8rem;
            color: #333;
            margin: 0;
        }

        .page-subtitle {
            color: var(--text-light);
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 8px 16px;
            background-color: var(--primary);
            color: white;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .btn:hover {
            background-color: #4a5ce0;
        }

        .btn-secondary {
            background-color: #f0f0f0;
            color: #333;
            border: 1px solid #ddd;
        }

        .btn-secondary:hover {
            background-color: #e0e0e0;
        }

        .chart-container {
            height: 400px;
            margin-bottom: 30px;
        }

        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 500;
            color: #333;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        .tab-container {
            margin-top: 20px;
        }

        .tab-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .tab-button {
            padding: 10px 20px;
            background-color: #f0f0f0;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .tab-button:hover {
            background-color: #e0e0e0;
        }

        .tab-button.active {
            background-color: var(--primary);
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .empty-state {
            text-align: center;
            padding: 30px;
            color: var(--text-light);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #ddd;
        }

        .empty-state p {
            font-size: 1rem;
            margin: 0;
        }
    </style>
</head>
<body>
    <!-- Include header -->
    <jsp:include page="adminHeader.jsp" />

    <div class="container">
        <div class="main-content">
            <div class="report-section">
                <div class="page-header">
                    <div>
                        <h1 class="page-title"><%= reportType %> Report</h1>
                        <p class="page-subtitle">Data from <%= startDate.format(formatter) %> to <%= endDate.format(formatter) %></p>
                    </div>
                    <div class="action-buttons">
                        <button onclick="window.print()" class="btn"><i class="fas fa-print"></i> Print Report</button>
                        <a href="${pageContext.request.contextPath}/admin/reports" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to Dashboard</a>
                    </div>
                </div>

                <% if (reportType.equals("Rental Activity")) { %>
                    <% Map<String, Object> report = (Map<String, Object>) request.getAttribute("report"); %>

                    <div class="tab-container">
                        <div class="tab-buttons">
                            <button class="tab-button active" onclick="showTab('chart')">Chart View</button>
                            <button class="tab-button" onclick="showTab('table')">Table View</button>
                        </div>

                        <div id="chart" class="tab-content active">
                            <div class="chart-container">
                                <canvas id="rentalChart"></canvas>
                            </div>
                        </div>

                        <div id="table" class="tab-content">
                            <table class="report-table">
                                <tr>
                                    <th>Total Rentals</th>
                                    <th>Active Rentals</th>
                                    <th>Completed Rentals</th>
                                    <th>Pending Rentals</th>
                                    <th>Total Revenue</th>
                                </tr>
                                <tr>
                                    <td><%= report.get("totalRentals") %></td>
                                    <td><%= report.get("activeRentals") %></td>
                                    <td><%= report.get("completedRentals") %></td>
                                    <td><%= report.get("pendingRentals") %></td>
                                    <td>₹<%= String.format("%.2f", report.get("totalRevenue")) %></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <script>
                        // Prepare data for chart
                        const rentalData = {
                            labels: ['Active Rentals', 'Completed Rentals', 'Pending Rentals'],
                            datasets: [{
                                label: 'Rental Status',
                                data: [<%= report.get("activeRentals") %>, <%= report.get("completedRentals") %>, <%= report.get("pendingRentals") %>],
                                backgroundColor: ['#36A2EB', '#4BC0C0', '#FFCD56'],
                                hoverOffset: 4
                            }]
                        };

                        // Create chart
                        const rentalCtx = document.getElementById('rentalChart').getContext('2d');
                        const rentalChart = new Chart(rentalCtx, {
                            type: 'pie',
                            data: rentalData,
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    title: {
                                        display: true,
                                        text: 'Rental Status Distribution',
                                        font: { size: 16 }
                                    },
                                    legend: {
                                        position: 'bottom'
                                    }
                                }
                            }
                        });
                    </script>
                <% } else if (reportType.equals("Popular Books")) { %>
                    <% List<Map<String, Object>> report = (List<Map<String, Object>>) request.getAttribute("report"); %>

                    <div class="tab-container">
                        <div class="tab-buttons">
                            <button class="tab-button active" onclick="showTab('chart')">Chart View</button>
                            <button class="tab-button" onclick="showTab('table')">Table View</button>
                        </div>

                        <div id="chart" class="tab-content active">
                            <div class="chart-container">
                                <canvas id="booksChart"></canvas>
                            </div>
                        </div>

                        <div id="table" class="tab-content">
                            <table class="report-table">
                                <thead>
                                    <tr>
                                        <th>Rank</th>
                                        <th>Book Title</th>
                                        <th>Author</th>
                                        <th>Rental Count</th>
                                        <th>Revenue</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% if (report != null && !report.isEmpty()) { %>
                                        <% int rank = 1; %>
                                        <% for (Map<String, Object> book : report) { %>
                                            <tr>
                                                <td><%= rank++ %></td>
                                                <td><%= book.get("title") %></td>
                                                <td><%= book.get("author") %></td>
                                                <td><%= book.get("rentalCount") %></td>
                                                <td>₹<%= String.format("%.2f", book.get("revenue")) %></td>
                                            </tr>
                                        <% } %>
                                    <% } else { %>
                                        <tr>
                                            <td colspan="5" style="text-align: center;">No data available for the selected period.</td>
                                        </tr>
                                    <% } %>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <% if (report != null && !report.isEmpty()) { %>
                    <script>
                        // Prepare data for chart
                        const bookLabels = [];
                        const bookData = [];
                        const bookColors = [
                            '#FF6384', '#36A2EB', '#FFCD56', '#4BC0C0', '#9966FF',
                            '#FF9F40', '#C9CBCF', '#7FC8F8', '#FFABAB', '#A5D6A7'
                        ];

                        <% int count = 0; %>
                        <% for (Map<String, Object> book : report) { %>
                            <% if (count < 5) { %> // Limit to top 5 for better visualization
                                bookLabels.push('<%= book.get("title") %>');
                                bookData.push(<%= book.get("rentalCount") %>);
                                <% count++; %>
                            <% } %>
                        <% } %>

                        // Create chart
                        const booksCtx = document.getElementById('booksChart').getContext('2d');
                        const booksChart = new Chart(booksCtx, {
                            type: 'bar',
                            data: {
                                labels: bookLabels,
                                datasets: [{
                                    label: 'Rental Count',
                                    data: bookData,
                                    backgroundColor: bookColors,
                                    borderWidth: 1
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    title: {
                                        display: true,
                                        text: 'Top 5 Most Popular Books',
                                        font: { size: 16 }
                                    },
                                    legend: {
                                        display: false
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        title: {
                                            display: true,
                                            text: 'Number of Rentals'
                                        }
                                    },
                                    x: {
                                        ticks: {
                                            maxRotation: 45,
                                            minRotation: 45
                                        }
                                    }
                                }
                            }
                        });
                    </script>
                    <% } %>
                <% } else if (reportType.equals("User Activity")) { %>
                    <% List<Map<String, Object>> report = (List<Map<String, Object>>) request.getAttribute("report"); %>

                    <div class="tab-container">
                        <div class="tab-buttons">
                            <button class="tab-button active" onclick="showTab('chart')">Chart View</button>
                            <button class="tab-button" onclick="showTab('table')">Table View</button>
                        </div>

                        <div id="chart" class="tab-content active">
                            <div class="chart-container">
                                <canvas id="usersChart"></canvas>
                            </div>
                        </div>

                        <div id="table" class="tab-content">
                            <table class="report-table">
                                <thead>
                                    <tr>
                                        <th>Rank</th>
                                        <th>Username</th>
                                        <th>Email</th>
                                        <th>Rental Count</th>
                                        <th>Total Spent</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% if (report != null && !report.isEmpty()) { %>
                                        <% int rank = 1; %>
                                        <% for (Map<String, Object> user : report) { %>
                                            <tr>
                                                <td><%= rank++ %></td>
                                                <td><%= user.get("username") %></td>
                                                <td><%= user.get("email") %></td>
                                                <td><%= user.get("rentalCount") %></td>
                                                <td>₹<%= String.format("%.2f", user.get("totalSpent")) %></td>
                                            </tr>
                                        <% } %>
                                    <% } else { %>
                                        <tr>
                                            <td colspan="5" style="text-align: center;">No data available for the selected period.</td>
                                        </tr>
                                    <% } %>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <% if (report != null && !report.isEmpty()) { %>
                    <script>
                        // Prepare data for chart
                        const userLabels = [];
                        const userData = [];
                        const spentData = [];

                        <% int count = 0; %>
                        <% for (Map<String, Object> user : report) { %>
                            <% if (count < 5) { %> // Limit to top 5 for better visualization
                                userLabels.push('<%= user.get("username") %>');
                                userData.push(<%= user.get("rentalCount") %>);
                                spentData.push(<%= user.get("totalSpent") %>);
                                <% count++; %>
                            <% } %>
                        <% } %>

                        // Create chart
                        const usersCtx = document.getElementById('usersChart').getContext('2d');
                        const usersChart = new Chart(usersCtx, {
                            type: 'bar',
                            data: {
                                labels: userLabels,
                                datasets: [{
                                    label: 'Rental Count',
                                    data: userData,
                                    backgroundColor: '#36A2EB',
                                    borderWidth: 1,
                                    yAxisID: 'y'
                                }, {
                                    label: 'Total Spent (₹)',
                                    data: spentData,
                                    backgroundColor: '#FF6384',
                                    borderWidth: 1,
                                    yAxisID: 'y1',
                                    type: 'line',
                                    fill: false,
                                    tension: 0.4
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    title: {
                                        display: true,
                                        text: 'Top 5 Most Active Users',
                                        font: { size: 16 }
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        position: 'left',
                                        title: {
                                            display: true,
                                            text: 'Number of Rentals'
                                        }
                                    },
                                    y1: {
                                        beginAtZero: true,
                                        position: 'right',
                                        grid: {
                                            drawOnChartArea: false
                                        },
                                        title: {
                                            display: true,
                                            text: 'Amount Spent (₹)'
                                        }
                                    }
                                }
                            }
                        });
                    </script>
                    <% } %>
                <% } %>
            </div>
        </div>

        <!-- Include footer -->
        <jsp:include page="footer.jsp" />
    </div>

    <script>
        // Function to switch between tabs
        function showTab(tabId) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Deactivate all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Show the selected tab content
            document.getElementById(tabId).classList.add('active');

            // Activate the clicked button
            event.target.classList.add('active');
        }
    </script>
</body>
</html>