<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<footer class="footer">
    <div class="footer-content">
        <div class="footer-left">
            <div class="footer-logo">
                <h2><PERSON>yan<PERSON>un<PERSON></h2>
            </div>
            <p class="footer-description">
                Your one-stop destination for knowledge and learning resources.
                Discover, learn, and grow with our extensive collection of books and materials.
            </p>
            <div class="social-links">
                <a href="#"><i class="fab fa-facebook-f"></i></a>
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-instagram"></i></a>
                <a href="#"><i class="fab fa-linkedin-in"></i></a>
            </div>
        </div>

        <div class="footer-right">
            <div class="footer-section">
                <h3>Quick Links</h3>
                <div class="footer-links">
                    <a href="${pageContext.request.contextPath}/index.jsp">Home</a>
                    <a href="${pageContext.request.contextPath}/View/category.jsp">Categories</a>
                    <a href="${pageContext.request.contextPath}/View/UserDashboard.jsp">User Dashboard</a>
                    <a href="${pageContext.request.contextPath}/View/about.jsp">About Us</a>
                    <a href="${pageContext.request.contextPath}/View/Contact.jsp">Contact Us</a>
                </div>
            </div>

            <div class="footer-section">
                <h3>Categories</h3>
                <div class="footer-links">
                    <a href="${pageContext.request.contextPath}/View/category.jsp#new">New Books</a>
                    <a href="${pageContext.request.contextPath}/View/category.jsp#old">Old Books</a>
                    <a href="${pageContext.request.contextPath}/View/category.jsp#recent">Recent Additions</a>
                    <a href="${pageContext.request.contextPath}/View/category.jsp#popular">Popular Books</a>
                </div>
            </div>

            <div class="footer-section">
                <h3>Help & Support</h3>
                <div class="footer-links">
                    <a href="#">FAQ</a>
                    <a href="#">Privacy Policy</a>
                    <a href="#">Terms of Service</a>
                    <a href="#">Support Center</a>
                </div>
            </div>
        </div>
    </div>

    <div class="footer-bottom">
        <p>&copy; 2024 GyanKunja. All rights reserved.</p>
    </div>
</footer>

<style>
    /* Footer Styles */
    .footer {
        background: #fff;
        padding: 2rem 5%;
        border-top: 1px solid #e0e7ff;
        margin-top: auto;
    }

    .footer-content {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        gap: 4rem;
        padding-bottom: 2rem;
    }

    .footer-left {
        max-width: 400px;
    }

    .footer-logo {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .footer-logo h2 {
        font-size: 1.25rem;
        color: #5b6dfa;
        font-weight: 600;
    }

    .footer-description {
        color: #666;
        font-size: 0.95rem;
        line-height: 1.5;
        margin-bottom: 1rem;
    }

    .footer-right {
        display: flex;
        gap: 4rem;
    }

    .footer-section {
        min-width: 140px;
    }

    .footer-section h3 {
        color: #5b6dfa;
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #e0e7ff;
    }

    .footer-links {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .footer-links a {
        color: #666;
        text-decoration: none;
        font-size: 0.95rem;
        transition: all 0.3s ease;
    }

    .footer-links a:hover {
        color: #5b6dfa;
        transform: translateX(5px);
    }

    .social-links {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
    }

    .social-links a {
        color: #666;
        text-decoration: none;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        padding: 0.5rem;
        border-radius: 50%;
        background: #f0f4ff;
    }

    .social-links a:hover {
        color: #fff;
        background: #5b6dfa;
        transform: translateY(-3px);
    }

    .footer-bottom {
        max-width: 1200px;
        margin: 0 auto;
        padding-top: 1rem;
        border-top: 1px solid #e0e7ff;
        text-align: center;
        color: #666;
        font-size: 0.9rem;
    }

    @media (max-width: 768px) {
        .footer-content {
            flex-direction: column;
            gap: 2rem;
        }

        .footer-right {
            flex-wrap: wrap;
            gap: 2rem;
        }

        .footer-section {
            min-width: 120px;
        }
    }
</style>