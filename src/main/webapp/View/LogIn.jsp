<%--
  Created by IntelliJ IDEA.
  User: nitro
  Date: 4/20/2025
  Time: 11:04 PM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>Login - <PERSON><PERSON>unja</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/responsive.css">
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --accent: #b388ff;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --input-bg: #f0f4ff;
            --input-border: #e0e7ff;
            --text-main: #222;
            --text-light: #666;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        html, body {
            height: 100%;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .main-content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(120deg, var(--primary) 0%, var(--secondary) 100%);
            padding: 150px 30px 80px 30px;
        }

        /* Navbar Styles */
        .navbar {
            background: var(--white);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow);
        }

        .nav-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem 5%;
            border-bottom: 1px solid var(--input-border);
        }

        .nav-bottom {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0.5rem 5%;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--text-main);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary);
            background: var(--input-bg);
        }

        .nav-links a.active {
            color: var(--primary);
            background: var(--input-bg);
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .search-section {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .search-container {
            display: flex;
            gap: 0.5rem;
        }

        .search-input {
            padding: 0.5rem 1rem;
            border: 1px solid var(--input-border);
            border-radius: 8px;
            width: 300px;
        }

        .search-button {
            background: var(--primary);
            color: var(--white);
            border: none;
            padding: 0.5rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
        }

        .nav-link {
            color: var(--text-main);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .login-btn {
            background: var(--primary);
            color: var(--white);
        }

        .register-btn {
            background: var(--accent);
            color: var(--white);
        }

        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        /* Login Page Styles */
        .login-card {
            background: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            padding: 1.7rem 1.2rem 1.2rem 1.2rem;
            max-width: 420px;
            width: 100%;
            margin: 1.5rem;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .login-title {
            font-size: 1.4rem;
            color: var(--primary);
            font-weight: 700;
            margin-bottom: 1.2rem;
            letter-spacing: 0.5px;
        }

        .login-desc {
            color: var(--text-light);
            font-size: 0.99rem;
            margin-bottom: 1.2rem;
            text-align: center;
        }

        .login-form {
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 1.3rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.7rem;
        }

        .form-group label {
            font-weight: 500;
            color: var(--text-main);
            font-size: 1rem;
        }

        .form-group input[type="email"], .form-group input[type="password"] {
            padding: 0.5rem 0.7rem;
            border: 1.5px solid var(--input-border);
            border-radius: 8px;
            font-size: 1rem;
            background: var(--input-bg);
            color: var(--text-main);
            outline: none;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-group input:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 2px #5b6dfa22;
        }

        .login-submit {
            background: linear-gradient(90deg, var(--primary) 60%, var(--accent) 100%);
            color: var(--white);
            border: none;
            padding: 0.7rem 0;
            border-radius: 8px;
            font-size: 1.07rem;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 2px 8px #5b6dfa22;
            margin-top: 0.5rem;
            transition: background 0.2s, box-shadow 0.2s, transform 0.1s;
        }

        .login-submit:hover {
            background: linear-gradient(90deg, var(--accent) 0%, var(--primary) 100%);
            transform: translateY(-2px) scale(1.025);
            box-shadow: 0 6px 20px #5b6dfa33;
        }

        /* Login Tabs Styles */
        .login-tabs {
            display: flex;
            margin-bottom: 1.5rem;
            border-bottom: 1px solid var(--input-border);
        }

        .login-tab {
            padding: 0.75rem 1.5rem;
            background: none;
            border: none;
            border-bottom: 2px solid transparent;
            color: var(--text-light);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            flex: 1;
            text-align: center;
        }

        .login-tab.active {
            color: var(--primary);
            border-bottom: 2px solid var(--primary);
        }

        .login-tab:hover {
            color: var(--primary);
        }

        .admin-login-btn {
            background: var(--accent);
        }

        .admin-login-btn:hover {
            background: linear-gradient(90deg, var(--primary) 0%, var(--accent) 100%);
        }

        .login-footer {
            margin-top: 0.7rem;
            text-align: center;
        }

        .create-account-link {
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
            margin-left: 0.3rem;
            transition: color 0.2s;
        }

        .create-account-link:hover {
            color: var(--accent);
            text-decoration: underline;
        }

        .error-message {
            background-color: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 500;
        }

        .success-message {
            background-color: #e8f5e9;
            color: #2e7d32;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 500;
        }

        @media (max-width: 600px) {
            .search-container {
                display: none;
            }

            .login-card {
                padding: 1rem 0.5rem 0.7rem 0.5rem;
            }

            .login-title {
                font-size: 1.05rem;
            }
        }

        /* Footer Styles */
        .footer {
            background: var(--white);
            padding: 2rem 5%;
            border-top: 1px solid var(--input-border);
            margin-top: auto;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            gap: 4rem;
            padding-bottom: 2rem;
        }

        .footer-left {
            max-width: 400px;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .footer-logo img {
            height: 30px;
        }

        .footer-logo h2 {
            font-size: 1.25rem;
            color: var(--primary);
            font-weight: 600;
        }

        .footer-description {
            color: var(--text-light);
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .footer-right {
            display: flex;
            gap: 4rem;
        }

        .footer-section {
            min-width: 140px;
        }

        .footer-section h3 {
            color: var(--primary);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--input-border);
        }

        .footer-links {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .footer-links a {
            color: var(--text-light);
            text-decoration: none;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .footer-links a:hover {
            color: var(--primary);
            transform: translateX(5px);
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .social-links a {
            color: var(--text-light);
            text-decoration: none;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            padding: 0.5rem;
            border-radius: 50%;
            background: var(--input-bg);
        }

        .social-links a:hover {
            color: var(--primary);
            background: var(--primary);
            color: var(--white);
            transform: translateY(-3px);
        }

        .footer-bottom {
            max-width: 1200px;
            margin: 0 auto;
            padding-top: 1rem;
            border-top: 1px solid var(--input-border);
            text-align: center;
            color: var(--text-light);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .footer-content {
                flex-direction: column;
                gap: 2rem;
            }

            .footer-right {
                flex-wrap: wrap;
                gap: 2rem;
            }

            .footer-section {
                min-width: 120px;
            }
        }
    </style>
</head>
<body>
<nav class="navbar">
    <div class="nav-top">
        <div class="logo-section">
            <img src="${pageContext.request.contextPath}/assets/images/books/Gyankunj.png" alt="GyanKunja Logo" style="height: 40px; width: auto;">
            <h1 class="website-name">GyanKunja</h1>
        </div>
        <div class="search-section">
            <div class="search-container">
                <input type="text" class="search-input" placeholder="Search...">
                <button class="search-button">Search</button>
            </div>
            <div class="auth-buttons">
                <a href="${pageContext.request.contextPath}/View/LogIn.jsp" class="nav-link login-btn">Login</a>
                <a href="${pageContext.request.contextPath}/ForRegister" class="nav-link register-btn">Register</a>
            </div>
        </div>
    </div>
    <button class="mobile-menu-toggle" id="mobileMenuToggle">
        <i class="fas fa-bars"></i>
    </button>
    <div class="nav-bottom" id="navBottom">
        <div class="nav-links">
            <a href="${pageContext.request.contextPath}/index.jsp" class="nav-link">Home</a>
            <a href="${pageContext.request.contextPath}/View/category.jsp" class="nav-link">Categories</a>
            <a href="${pageContext.request.contextPath}/View/UserDashboard.jsp" class="nav-link">User Dashboard</a>
            <a href="${pageContext.request.contextPath}/View/Contact.jsp" class="nav-link">Contact Us</a>
            <a href="${pageContext.request.contextPath}/View/about.jsp" class="nav-link">About Us</a>
        </div>
    </div>
</nav>

<main class="main-content">
    <div class="login-card">
        <h2 class="login-title">Welcome Back</h2>
        <div class="login-desc">Log in to continue your e-learning journey!</div>
        <% if (request.getAttribute("errorMessage") != null) { %>
            <div class="error-message">
                <%= request.getAttribute("errorMessage") %>
            </div>
        <% } %>
        <% if (request.getAttribute("successMessage") != null) { %>
            <div class="success-message">
                <%= request.getAttribute("successMessage") %>
            </div>
        <% } %>

        <form class="login-form" action="${pageContext.request.contextPath}/ForLogIn" method="post">
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" placeholder="Enter your email" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" placeholder="Enter your password" required>
            </div>
            <button type="submit" class="login-submit">Login</button>
        </form>
        <div class="login-footer">
            Don't have an account? <a href="${pageContext.request.contextPath}/ForRegister" class="create-account-link">Create Account</a>
        </div>
    </div>
</main>

<jsp:include page="footer.jsp" />

<script>
    // Mobile menu toggle
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const navBottom = document.getElementById('navBottom');

    mobileMenuToggle.addEventListener('click', function() {
        navBottom.classList.toggle('active');
    });

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(event) {
        if (!navBottom.contains(event.target) && !mobileMenuToggle.contains(event.target)) {
            navBottom.classList.remove('active');
        }
    });
</script>
</body>
</html>
