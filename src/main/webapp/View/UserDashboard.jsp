<%--
  Created by IntelliJ IDEA.
  User: nitro
  Date: 4/21/2025
  Time: 10:18 AM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null) {
        request.setAttribute("errorMessage", "Please login first to access your dashboard");
        request.getRequestDispatcher("LogIn.jsp").forward(request, response);
        return;
    }
%>
<html>
<head>
    <title>User Dashboard - GyanKunja</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/responsive.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --accent: #b388ff;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --input-bg: #f0f4ff;
            --input-border: #e0e7ff;
            --text-main: #222;
            --text-light: #666;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        html, body {
            height: 100%;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        /* Navbar Styles */
        .navbar {
            background: var(--white);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow);
        }

        .nav-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem 5%;
            border-bottom: 1px solid var(--input-border);
            gap: 3rem;
        }

        .nav-bottom {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0.5rem 5%;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--text-main);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary);
            background: var(--input-bg);
        }

        .nav-links a.active {
            color: var(--primary);
            background: var(--input-bg);
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .search-section {
            display: flex;
            align-items: center;
            gap: 2rem;
            width: 100%;
            justify-content: flex-end;
            margin-left: 2.5rem;
        }

        .search-container {
            display: flex;
            gap: 0.5rem;
            margin-right: auto;
        }

        .search-input {
            padding: 0.5rem 1rem;
            border: 1px solid var(--input-border);
            border-radius: 8px;
            width: 300px;
        }

        .search-button {
            background: var(--primary);
            color: var(--white);
            border: none;
            padding: 0.5rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
            margin-left: auto;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-left: auto;
        }

        .username {
            color: var(--primary);
            font-weight: 500;
            font-size: 1rem;
        }

        .logout-btn {
            background: var(--primary);
            color: var(--white);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s;
        }

        .logout-btn:hover {
            background: var(--accent);
            transform: translateY(-2px);
        }

        .logout-btn i {
            font-size: 1.1rem;
        }

        /* Dashboard Styles */
        .dashboard-container {
            max-width: 1200px;
            margin: 120px auto 50px;
            padding: 0 20px;
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 30px;
        }

        .sidebar {
            background: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            padding: 30px;
            height: fit-content;
            position: sticky;
            top: 30px;
        }

        .profile-section {
            text-align: center;
            padding-bottom: 30px;
            border-bottom: 1px solid var(--input-border);
            margin-bottom: 30px;
            position: relative;
            margin-top: 2.5rem;
        }

        .profile-pic {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 20px;
            background: var(--input-bg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: var(--primary);
            border: 3px solid var(--primary);
            position: relative;
            overflow: hidden;
        }

        .profile-pic img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-pic-upload {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 5px;
            font-size: 0.8rem;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .profile-pic:hover .profile-pic-upload {
            opacity: 1;
        }

        .profile-pic-input {
            display: none;
        }

        .profile-name {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }

        .profile-email {
            color: var(--text-light);
            font-size: 1rem;
        }

        .main-content {
            background: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            padding: 40px;
            margin-top: 2.5rem;
        }

        .header-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--primary);
        }

        .edit-btn {
            padding: 10px 20px;
            background: var(--primary);
            color: var(--white);
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-left: 1.5rem;
        }

        .edit-btn:hover {
            background: #4a5bd9;
            transform: translateY(-2px);
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 10px;
            font-weight: 500;
            color: var(--text-main);
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 1px solid var(--input-border);
            border-radius: 8px;
            background: var(--input-bg);
            font-size: 1rem;
            transition: all 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(91, 109, 250, 0.1);
        }

        .form-group input[readonly] {
            background: #f5f5f5;
            cursor: not-allowed;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: var(--primary);
            color: var(--white);
        }

        .btn-primary:hover {
            background: #4a5bd9;
            transform: translateY(-2px);
        }

        .error-message {
            color: #ff4444;
            background: #ffebee;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .success-message {
            color: #00C851;
            background: #e8f5e9;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .books-section {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid var(--input-border);
        }

        .books-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .book-card {
            background: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            overflow: hidden;
            transition: all 0.3s;
        }

        .book-card:hover {
            transform: translateY(-5px);
        }

        .book-image {
            width: 100%;
            height: 250px;
            object-fit: cover;
        }

        .book-info {
            padding: 15px;
        }

        .book-title {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .book-author {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        /* Footer Styles */
        .footer {
            background: var(--white);
            padding: 2rem 5%;
            border-top: 1px solid var(--input-border);
            margin-top: auto;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            gap: 4rem;
            padding-bottom: 2rem;
        }

        .footer-left {
            max-width: 400px;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .footer-logo img {
            height: 30px;
        }

        .footer-logo h2 {
            font-size: 1.25rem;
            color: var(--primary);
            font-weight: 600;
        }

        .footer-description {
            color: var(--text-light);
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .footer-right {
            display: flex;
            gap: 4rem;
        }

        .footer-section {
            min-width: 140px;
        }

        .footer-section h3 {
            color: var(--primary);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--input-border);
        }

        .footer-links {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .footer-links a {
            color: var(--text-light);
            text-decoration: none;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .footer-links a:hover {
            color: var(--primary);
            transform: translateX(5px);
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .social-links a {
            color: var(--text-light);
            text-decoration: none;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            padding: 0.5rem;
            border-radius: 50%;
            background: var(--input-bg);
        }

        .social-links a:hover {
            color: var(--primary);
            background: var(--primary);
            color: var(--white);
            transform: translateY(-3px);
        }

        .footer-bottom {
            max-width: 1200px;
            margin: 0 auto;
            padding-top: 1rem;
            border-top: 1px solid var(--input-border);
            text-align: center;
            color: var(--text-light);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .footer-content {
                flex-direction: column;
                gap: 2rem;
            }

            .footer-right {
                flex-wrap: wrap;
                gap: 2rem;
            }

            .footer-section {
                min-width: 120px;
            }
        }

        /* Sidebar Navigation Vertical Layout */
        .sidebar .nav-links {
            display: flex;
            flex-direction: column;
            align-items: stretch;
            gap: 0.5rem;
            margin-top: 1.5rem;
        }
        .sidebar .nav-link {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px 20px;
            border-radius: 8px;
            color: var(--text-main);
            text-decoration: none;
            transition: all 0.3s;
            font-size: 1rem;
            width: 100%;
            background: transparent;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background: var(--input-bg);
            color: var(--primary);
            transform: translateX(5px);
        }
        .sidebar .nav-link i {
            font-size: 1.2rem;
        }

        /* Mobile menu toggle */
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            color: var(--primary);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 10px;
            position: absolute;
            right: 20px;
            top: 15px;
        }

        /* Responsive styles */
        @media (max-width: 992px) {
            .dashboard-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                max-width: 100%;
                margin-bottom: 20px;
            }

            .main-content {
                width: 100%;
            }

            .mobile-menu-toggle {
                display: block;
            }

            .nav-bottom {
                display: none;
            }

            .nav-bottom.active {
                display: block;
            }

            .nav-links {
                flex-direction: column;
            }

            .nav-link {
                padding: 12px 20px;
                border-bottom: 1px solid #eee;
            }
        }

        @media (max-width: 768px) {
            .profile-section {
                flex-direction: column;
                align-items: center;
                text-align: center;
            }

            .profile-info {
                margin-left: 0;
                margin-top: 15px;
            }

            .dashboard-stats {
                grid-template-columns: 1fr 1fr;
            }

            .book-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            }
        }

        @media (max-width: 576px) {
            .dashboard-stats {
                grid-template-columns: 1fr;
            }

            .book-grid {
                grid-template-columns: 1fr;
            }

            .search-section {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
                margin-left: 0;
            }

            .search-container {
                width: 100%;
                margin-right: 0;
            }

            .search-input {
                width: 100%;
            }

            .auth-buttons {
                width: 100%;
                justify-content: flex-end;
            }
        }
    </style>
</head>
<body>
    <jsp:include page="header.jsp" />

    <div class="dashboard-container">
        <div class="sidebar">
            <div class="profile-section">
                <form action="${pageContext.request.contextPath}/UpdateProfilePicture" method="post" enctype="multipart/form-data" onsubmit="return validateFileSize()">
                <div class="profile-pic">
                        <%
                            String profilePicture = user.getProfilePicture();
                            System.out.println("Profile picture in JSP: " + (profilePicture != null ? profilePicture.length() : 0));
                            if (profilePicture != null && !profilePicture.isEmpty()) {
                        %>
                            <img src="data:image/*;base64,<%= profilePicture %>" alt="Profile Picture" onerror="console.error('Error loading profile picture')">
                        <% } else { %>
                            <i class="fas fa-user"></i>
                        <% } %>
                        <label class="profile-pic-upload" for="profile-pic-input">Change Photo</label>
                        <input type="file" id="profile-pic-input" name="profilePicture" class="profile-pic-input" accept="image/*" onchange="validateFileSize()">
        </div>
                    <div class="error-message" id="fileError"></div>
                </form>
                <h2 class="profile-name"><%= user.getUserName() %></h2>
                <p class="profile-email"><%= user.getEmail() %></p>
            </div>
            <button class="mobile-menu-toggle" id="mobileMenuToggle">
                <i class="fas fa-bars"></i>
            </button>
            <div class="nav-links" id="navLinks">
                <a href="#" class="nav-link active">
                    <i class="fas fa-user"></i> Profile
                </a>
                <a href="#" class="nav-link">
                    <i class="fas fa-book"></i> My Books
                </a>
                <a href="#" class="nav-link">
                    <i class="fas fa-history"></i> History
                </a>
                <a href="#" class="nav-link">
                    <i class="fas fa-cog"></i> Settings
                </a>
                <a href="${pageContext.request.contextPath}/ForLogOut" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>

<div class="main-content">
            <div class="header-section">
                <h2 class="section-title">Personal Information</h2>
                <button class="edit-btn" id="editToggle">
                    <i class="fas fa-edit"></i> Edit Profile
                </button>
            </div>

            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>

            <form id="profileForm" action="../profile/update" method="post" enctype="application/x-www-form-urlencoded">
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" value="<%= user.getUserName() %>" readonly>
                        </div>

                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" value="<%= user.getEmail() %>" readonly>
                        </div>

                <div class="form-group">
                    <label for="userType">User Type</label>
                    <input type="text" id="userType" name="userType" value="<%= user.getUserType() %>" readonly>
                </div>

                <div class="form-group">
                    <button type="button" id="changePasswordBtn" class="btn btn-secondary" style="display: none;">
                        <i class="fas fa-key"></i> Change Password
                    </button>
                </div>

                <div id="passwordFields" style="display: none;">
                    <div class="form-group">
                        <label for="currentPassword">Current Password</label>
                        <input type="password" id="currentPassword" name="currentPassword" readonly>
                    </div>

                    <div class="form-group">
                        <label for="newPassword">New Password</label>
                        <input type="password" id="newPassword" name="newPassword" readonly>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword">Confirm New Password</label>
                        <input type="password" id="confirmPassword" name="confirmPassword" readonly>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary" id="saveBtn" style="display: none;">Save Changes</button>
            </form>

            <div class="books-section">
                <div class="header-section">
                    <h2 class="section-title">My Rentals</h2>
                    <a href="${pageContext.request.contextPath}/user-rentals" class="edit-btn">
                        <i class="fas fa-book"></i> View All Rentals
                    </a>
                </div>
                <p>View and manage your book rentals. You can edit pending rentals before they are approved by the admin.</p>
                <div class="action-buttons" style="margin-top: 20px;">
                    <a href="${pageContext.request.contextPath}/user-rentals" class="btn btn-primary">
                        <i class="fas fa-list"></i> My Rentals
                    </a>
                    <a href="${pageContext.request.contextPath}/index.jsp" class="btn btn-secondary">
                        <i class="fas fa-book"></i> Browse Books
                    </a>
                </div>
            </div>

            <div class="books-section">
                <h2 class="section-title">Recommended Books</h2>
                <div class="books-grid">
                    <div class="book-card">
                        <img src="https://images.unsplash.com/photo-1543002604-0a00d0b0b9e0?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" alt="Book 1" class="book-image">
                        <div class="book-info">
                            <div class="book-title">The Great Gatsby</div>
                            <div class="book-author">F. Scott Fitzgerald</div>
                        </div>
                    </div>
                    <div class="book-card">
                        <img src="https://images.unsplash.com/photo-1543002604-0a00d0b0b9e0?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" alt="Book 2" class="book-image">
                        <div class="book-info">
                            <div class="book-title">To Kill a Mockingbird</div>
                            <div class="book-author">Harper Lee</div>
                        </div>
                    </div>
                    <div class="book-card">
                        <img src="https://images.unsplash.com/photo-1543002604-0a00d0b0b9e0?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60" alt="Book 3" class="book-image">
                        <div class="book-info">
                            <div class="book-title">1984</div>
                            <div class="book-author">George Orwell</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer will be included at the end of the page -->

    <script>
        const editToggle = document.getElementById('editToggle');
        const saveBtn = document.getElementById('saveBtn');
        const inputs = document.querySelectorAll('#profileForm input:not([readonly])');
        let isEditMode = false;

        editToggle.addEventListener('click', function() {
            isEditMode = !isEditMode;

            // Make username and email fields editable
            const editableInputs = document.querySelectorAll('#username, #email');
            editableInputs.forEach(input => {
                input.readOnly = !isEditMode;
                input.style.backgroundColor = isEditMode ? 'var(--white)' : 'var(--input-bg)';
            });

            // Show/hide change password button
            const changePasswordBtn = document.getElementById('changePasswordBtn');
            changePasswordBtn.style.display = isEditMode ? 'block' : 'none';

            // Hide password fields when exiting edit mode
            if (!isEditMode) {
                document.getElementById('passwordFields').style.display = 'none';
                const passwordInputs = document.querySelectorAll('#currentPassword, #newPassword, #confirmPassword');
                passwordInputs.forEach(input => {
                    input.value = '';
                    input.readOnly = true;
                    input.style.backgroundColor = 'var(--input-bg)';
                });
            }

            saveBtn.style.display = isEditMode ? 'block' : 'none';
            editToggle.innerHTML = isEditMode ?
                '<i class="fas fa-times"></i> Cancel' :
                '<i class="fas fa-edit"></i> Edit Profile';
        });

        // Handle change password button click
        document.getElementById('changePasswordBtn').addEventListener('click', function() {
            const passwordFields = document.getElementById('passwordFields');
            const isPasswordFieldsVisible = passwordFields.style.display === 'block';

            passwordFields.style.display = isPasswordFieldsVisible ? 'none' : 'block';

            const passwordInputs = document.querySelectorAll('#currentPassword, #newPassword, #confirmPassword');
            passwordInputs.forEach(input => {
                input.readOnly = isPasswordFieldsVisible;
                input.style.backgroundColor = isPasswordFieldsVisible ? 'var(--input-bg)' : 'var(--white)';
                if (isPasswordFieldsVisible) {
                    input.value = '';
                }
            });

            this.innerHTML = isPasswordFieldsVisible ?
                '<i class="fas fa-key"></i> Change Password' :
                '<i class="fas fa-times"></i> Cancel Password Change';
        });

        document.getElementById('profileForm').addEventListener('submit', function(e) {
            if (!isEditMode) {
                e.preventDefault();
                return;
            }

            e.preventDefault();

            const formData = new FormData(this);
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');

            // Validate passwords if they are being changed
            const newPassword = formData.get('newPassword');
            const confirmPassword = formData.get('confirmPassword');
            const currentPassword = formData.get('currentPassword');

            if (newPassword || confirmPassword || currentPassword) {
                if (!newPassword || !confirmPassword || !currentPassword) {
                    errorMessage.textContent = 'All password fields are required';
                    errorMessage.style.display = 'block';
                    return;
                }

                if (newPassword !== confirmPassword) {
                    errorMessage.textContent = 'New passwords do not match';
                    errorMessage.style.display = 'block';
                    return;
                }
            }

            // Show loading state
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

            fetch('../profile/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams(formData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    errorMessage.style.display = 'none';
                    successMessage.textContent = 'Profile updated successfully!';
                    successMessage.style.display = 'block';

                    // Update the displayed values
                    document.getElementById('username').value = data.updatedUser.username || document.getElementById('username').value;
                    document.getElementById('email').value = data.updatedUser.email || document.getElementById('email').value;

                    // Exit edit mode
                    isEditMode = false;
                    editToggle.click();

                    // Reset button state
                    saveBtn.disabled = false;
                    saveBtn.innerHTML = 'Save Changes';

                    // Hide success message after 3 seconds
                    setTimeout(() => {
                        successMessage.style.display = 'none';
                    }, 3000);
                } else {
                    throw new Error(data.message || 'Failed to update profile');
                }
            })
            .catch(error => {
                successMessage.style.display = 'none';
                errorMessage.textContent = error.message || 'An error occurred while updating your profile.';
                errorMessage.style.display = 'block';

                // Reset button state
                saveBtn.disabled = false;
                saveBtn.innerHTML = 'Save Changes';

                // Hide error message after 3 seconds
                setTimeout(() => {
                    errorMessage.style.display = 'none';
                }, 3000);
            });
        });

        function validateFileSize() {
            const fileInput = document.getElementById('profile-pic-input');
            const fileError = document.getElementById('fileError');
            const maxSize = 5 * 1024 * 1024; // 5MB in bytes

            if (fileInput.files.length > 0) {
                const file = fileInput.files[0];
                if (file.size > maxSize) {
                    fileError.textContent = 'File size exceeds 5MB limit. Please choose a smaller image.';
                    fileError.style.display = 'block';
                    fileInput.value = ''; // Clear the file input
                    return false;
                } else {
                    fileError.style.display = 'none';
                    return true;
                }
            }
            return true;
        }

        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const navBottom = document.getElementById('navBottom');

        if (mobileMenuToggle && navBottom) {
            mobileMenuToggle.addEventListener('click', function() {
                navBottom.classList.toggle('active');
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', function(event) {
                if (!navBottom.contains(event.target) && !mobileMenuToggle.contains(event.target)) {
                    navBottom.classList.remove('active');
                }
            });
        }
    </script>

    <!-- Footer -->
    <jsp:include page="footer.jsp" />
</body>
</html>
