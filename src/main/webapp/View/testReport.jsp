<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
        response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Report - GyanKunja Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --text-main: #222;
            --text-light: #666;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }
        
        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .main-content {
            padding: 20px 0;
        }
        
        .report-section {
            background: var(--white);
            border-radius: var(--radius);
            padding: 20px;
            box-shadow: var(--shadow);
            margin-bottom: 30px;
        }
        
        .page-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 20px;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 8px 16px;
            background-color: var(--primary);
            color: white;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-top: 20px;
        }
        
        .btn:hover {
            background-color: #4a5ce0;
        }
    </style>
</head>
<body>
    <!-- Include header -->
    <jsp:include page="adminHeader.jsp" />
    
    <div class="container">
        <div class="main-content">
            <div class="report-section">
                <h1 class="page-title">Test Report Page</h1>
                <p>This is a test page to verify that the report functionality is working correctly.</p>
                <p>If you can see this page, it means the servlet is properly configured and can forward to JSP files.</p>
                
                <h2>Report Links</h2>
                <ul>
                    <li><a href="${pageContext.request.contextPath}/admin/reports/user-activity">User Activity Report</a></li>
                    <li><a href="${pageContext.request.contextPath}/admin/reports/popular-books">Popular Books Report</a></li>
                    <li><a href="${pageContext.request.contextPath}/admin/reports/rental-activity">Rental Activity Report</a></li>
                </ul>
                
                <a href="${pageContext.request.contextPath}/admin/reports" class="btn">
                    <i class="fas fa-arrow-left"></i> Back to Reports Dashboard
                </a>
            </div>
        </div>
    </div>
    
    <!-- Include footer -->
    <jsp:include page="footer.jsp" />
</body>
</html>
