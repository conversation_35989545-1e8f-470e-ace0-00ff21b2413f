<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%@ page import="java.util.List" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
        response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
        return;
    }

    List<User> users = (List<User>) request.getAttribute("users");
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Manage Users</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --accent: #b388ff;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --input-bg: #f0f4ff;
            --input-border: #e0e7ff;
            --text-main: #222;
            --text-light: #666;
            --success: #4CAF50;
            --warning: #FF9800;
            --danger: #F44336;
            --info: #2196F3;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            max-width: 1200px;
            margin: 120px auto 50px;
            padding: 20px;
            flex: 1;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .admin-title {
            font-size: 24px;
            color: var(--primary);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background-color: var(--primary);
            color: var(--white);
        }

        .btn-primary:hover {
            background-color: #4a5bd9;
            transform: translateY(-2px);
        }

        .btn-danger {
            background-color: var(--danger);
            color: var(--white);
        }

        .btn-danger:hover {
            background-color: #d32f2f;
            transform: translateY(-2px);
        }

        .btn-warning {
            background-color: var(--warning);
            color: var(--white);
        }

        .btn-warning:hover {
            background-color: #f57c00;
            transform: translateY(-2px);
        }

        .btn-success {
            background-color: var(--success);
            color: var(--white);
        }

        .btn-success:hover {
            background-color: #388e3c;
            transform: translateY(-2px);
        }

        .table-container {
            background-color: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--input-border);
        }

        th {
            background-color: var(--input-bg);
            color: var(--primary);
            font-weight: 600;
        }

        tr:hover {
            background-color: var(--input-bg);
        }

        .actions {
            display: flex;
            gap: 8px;
        }

        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .badge-admin {
            background-color: rgba(91, 109, 250, 0.1);
            color: var(--primary);
            border: 1px solid var(--primary);
        }

        .badge-user {
            background-color: rgba(52, 201, 201, 0.1);
            color: var(--secondary);
            border: 1px solid var(--secondary);
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-success {
            background-color: rgba(76, 175, 80, 0.1);
            color: var(--success);
            border: 1px solid var(--success);
        }

        .alert-error {
            background-color: rgba(244, 67, 54, 0.1);
            color: var(--danger);
            border: 1px solid var(--danger);
        }

        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: var(--white);
            margin: 10% auto;
            padding: 20px;
            border-radius: var(--radius);
            width: 400px;
            box-shadow: var(--shadow);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 18px;
            color: var(--primary);
        }

        .close {
            color: var(--text-light);
            font-size: 24px;
            cursor: pointer;
        }

        .modal-body {
            margin-bottom: 20px;
        }

        .modal-body ul {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        .warning {
            color: var(--danger);
            padding: 10px;
            background-color: rgba(244, 67, 54, 0.1);
            border-radius: 4px;
            border-left: 4px solid var(--danger);
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
    </style>
</head>
<body>
    <jsp:include page="adminHeader.jsp" />

    <div class="container">
        <a href="${pageContext.request.contextPath}/View/AdminDashboard.jsp" class="back-link">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>

        <div class="admin-header">
            <h1 class="admin-title">Manage Users</h1>
        </div>

        <% if (session.getAttribute("successMessage") != null) { %>
            <div class="alert alert-success">
                <%= session.getAttribute("successMessage") %>
            </div>
            <% session.removeAttribute("successMessage"); %>
        <% } %>

        <% if (session.getAttribute("errorMessage") != null) { %>
            <div class="alert alert-error">
                <%= session.getAttribute("errorMessage") %>
            </div>
            <% session.removeAttribute("errorMessage"); %>
        <% } %>

        <% if (request.getAttribute("successMessage") != null) { %>
            <div class="alert alert-success">
                <%= request.getAttribute("successMessage") %>
            </div>
        <% } %>

        <% if (request.getAttribute("errorMessage") != null) { %>
            <div class="alert alert-error">
                <%= request.getAttribute("errorMessage") %>
            </div>
        <% } %>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Username</th>
                        <th>Email</th>
                        <th>Full Name</th>
                        <th>User Type</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <% if (users != null && !users.isEmpty()) { %>
                        <% for (User u : users) { %>
                            <tr>
                                <td><%= u.getUserId() %></td>
                                <td><%= u.getUserName() %></td>
                                <td><%= u.getEmail() %></td>
                                <td><%= u.getFullName() != null ? u.getFullName() : u.getUserName() %></td>
                                <td>
                                    <% if ("admin".equalsIgnoreCase(u.getUserType())) { %>
                                        <span class="badge badge-admin">Admin</span>
                                    <% } else { %>
                                        <span class="badge badge-user">User</span>
                                    <% } %>
                                </td>
                                <td class="actions">
                                    <a href="${pageContext.request.contextPath}/admin/users/edit/<%= u.getUserId() %>" class="btn btn-warning">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <button class="btn btn-danger" onclick="confirmDelete(<%= u.getUserId() %>, '<%= u.getUserName() %>')">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </td>
                            </tr>
                        <% } %>
                    <% } else { %>
                        <tr>
                            <td colspan="6" style="text-align: center;">No users found</td>
                        </tr>
                    <% } %>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Confirm Delete</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete user <strong><span id="deleteUserName"></span></strong>?</p>
                <p>This action will permanently remove the user and all associated data including:</p>
                <ul>
                    <li>User account information</li>
                    <li>Rental history</li>
                    <li>All associated records</li>
                </ul>
                <p class="warning"><strong>Warning:</strong> This action cannot be undone!</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                <form id="deleteForm" action="${pageContext.request.contextPath}/admin/deleteUser" method="post">
                    <input type="hidden" id="deleteUserId" name="userId" value="">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>

    <jsp:include page="footer.jsp" />

    <script>
        // Delete confirmation modal
        function confirmDelete(userId, userName) {
            console.log('Confirming delete for user ID: ' + userId + ', name: ' + userName);
            document.getElementById('deleteUserId').value = userId;
            document.getElementById('deleteUserName').textContent = userName;
            document.getElementById('deleteModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('deleteModal').style.display = 'none';
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('deleteModal');
            if (event.target == modal) {
                closeModal();
            }
        }

        // Submit delete form with AJAX
        document.addEventListener('DOMContentLoaded', function() {
            const deleteForm = document.getElementById('deleteForm');
            if (deleteForm) {
                deleteForm.addEventListener('submit', function(e) {
                    e.preventDefault(); // Prevent the default form submission

                    const userId = document.getElementById('deleteUserId').value;
                    console.log('Deleting user ID: ' + userId);

                    // Create a form data object
                    const formData = new FormData();
                    formData.append('userId', userId);

                    // Create an XMLHttpRequest
                    const xhr = new XMLHttpRequest();
                    xhr.open('POST', deleteForm.action, true);
                    xhr.onload = function() {
                        if (xhr.status === 200) {
                            console.log('Delete request successful: ' + xhr.responseText);
                            closeModal();
                            alert('User deleted successfully!');
                            window.location.reload(); // Reload the page
                        } else {
                            console.error('Delete request failed: ' + xhr.status + ' ' + xhr.responseText);
                            closeModal();
                            alert('Failed to delete user: ' + xhr.responseText);
                        }
                    };
                    xhr.onerror = function() {
                        console.error('Delete request error');
                        closeModal();
                        alert('An error occurred while deleting the user. Please try again.');
                    };
                    xhr.send(formData);
                });
            }
        });
    </script>
</body>
</html>
