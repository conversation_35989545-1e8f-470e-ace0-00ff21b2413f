<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
        response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
        return;
    }

    User editUser = (User) request.getAttribute("editUser");
    if (editUser == null) {
        response.sendRedirect(request.getContextPath() + "/admin/users");
        return;
    }
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Edit User</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --accent: #b388ff;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --input-bg: #f0f4ff;
            --input-border: #e0e7ff;
            --text-main: #222;
            --text-light: #666;
            --success: #4CAF50;
            --warning: #FF9800;
            --danger: #F44336;
            --info: #2196F3;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            max-width: 800px;
            margin: 120px auto 50px;
            padding: 20px;
            flex: 1;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .admin-title {
            font-size: 24px;
            color: var(--primary);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background-color: var(--primary);
            color: var(--white);
        }

        .btn-primary:hover {
            background-color: #4a5bd9;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background-color: var(--input-bg);
            color: var(--text-main);
        }

        .btn-secondary:hover {
            background-color: #e0e7ff;
            transform: translateY(-2px);
        }

        .form-container {
            background-color: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-main);
        }

        .form-control {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid var(--input-border);
            border-radius: 8px;
            background-color: var(--input-bg);
            font-size: 14px;
            transition: all 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(91, 109, 250, 0.1);
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 30px;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-success {
            background-color: rgba(76, 175, 80, 0.1);
            color: var(--success);
            border: 1px solid var(--success);
        }

        .alert-error {
            background-color: rgba(244, 67, 54, 0.1);
            color: var(--danger);
            border: 1px solid var(--danger);
        }

        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        .user-info {
            background-color: var(--input-bg);
            padding: 20px;
            border-radius: var(--radius);
            margin-bottom: 30px;
        }

        .user-info h2 {
            color: var(--primary);
            margin-bottom: 10px;
        }

        .user-info p {
            color: var(--text-light);
            margin-bottom: 5px;
        }

        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 5px;
        }

        .badge-admin {
            background-color: rgba(91, 109, 250, 0.1);
            color: var(--primary);
            border: 1px solid var(--primary);
        }

        .badge-user {
            background-color: rgba(52, 201, 201, 0.1);
            color: var(--secondary);
            border: 1px solid var(--secondary);
        }
    </style>
</head>
<body>
    <jsp:include page="adminHeader.jsp" />

    <div class="container">
        <a href="${pageContext.request.contextPath}/admin/users" class="back-link">
            <i class="fas fa-arrow-left"></i> Back to Users
        </a>

        <div class="admin-header">
            <h1 class="admin-title">Edit User</h1>
        </div>

        <% if (request.getAttribute("successMessage") != null) { %>
            <div class="alert alert-success">
                <%= request.getAttribute("successMessage") %>
            </div>
        <% } %>

        <% if (request.getAttribute("errorMessage") != null) { %>
            <div class="alert alert-error">
                <%= request.getAttribute("errorMessage") %>
            </div>
        <% } %>

        <div class="user-info">
            <h2><%= editUser.getUserName() %></h2>
            <p>User ID: <%= editUser.getUserId() %></p>
            <p>Email: <%= editUser.getEmail() %></p>
            <p>User Type:
                <% if ("admin".equalsIgnoreCase(editUser.getUserType())) { %>
                    <span class="badge badge-admin">Admin</span>
                <% } else { %>
                    <span class="badge badge-user">User</span>
                <% } %>
            </p>
        </div>

        <div class="form-container">
            <form action="${pageContext.request.contextPath}/admin/users" method="post">
                <input type="hidden" name="action" value="update">
                <input type="hidden" name="userId" value="<%= editUser.getUserId() %>">

                <div class="form-group">
                    <label for="username">Username *</label>
                    <input type="text" id="username" name="username" class="form-control" value="<%= editUser.getUserName() %>" required>
                </div>

                <div class="form-group">
                    <label for="email">Email *</label>
                    <input type="email" id="email" name="email" class="form-control" value="<%= editUser.getEmail() %>" required>
                </div>

                <div class="form-group">
                    <label for="fullName">Full Name</label>
                    <input type="text" id="fullName" name="fullName" class="form-control" value="<%= editUser.getFullName() != null ? editUser.getFullName() : editUser.getUserName() %>">
                </div>

                <div class="form-group">
                    <label for="userType">User Type *</label>
                    <select id="userType" name="userType" class="form-control" required>
                        <option value="user" <%= "user".equalsIgnoreCase(editUser.getUserType()) ? "selected" : "" %>>User</option>
                        <option value="admin" <%= "admin".equalsIgnoreCase(editUser.getUserType()) ? "selected" : "" %>>Admin</option>
                    </select>
                </div>

                <div class="form-actions">
                    <a href="${pageContext.request.contextPath}/admin/users" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn btn-primary">Update User</button>
                </div>
            </form>
        </div>
    </div>

    <jsp:include page="footer.jsp" />
</body>
</html>
