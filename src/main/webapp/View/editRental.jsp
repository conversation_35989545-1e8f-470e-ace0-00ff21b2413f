<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%@ page import="Model.RentalDetails" %>
<%@ page import="java.time.format.DateTimeFormatter" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null) {
        request.setAttribute("errorMessage", "Please login first to access your rentals");
        request.getRequestDispatcher("LogIn.jsp").forward(request, response);
        return;
    }

    RentalDetails rental = (RentalDetails) request.getAttribute("rental");
    if (rental == null) {
        response.sendRedirect(request.getContextPath() + "/user-rentals");
        return;
    }

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Rental - GyanKunja</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/responsive.css">
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --accent: #b388ff;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --input-bg: #f0f4ff;
            --input-border: #e0e7ff;
            --text-main: #222;
            --text-light: #666;
            --success: #4CAF50;
            --warning: #FF9800;
            --danger: #F44336;
            --info: #2196F3;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            max-width: 800px;
            margin: 30px auto 50px;
            padding: 40px;
            background: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            flex: 1;
        }

        .page-title {
            font-size: 2rem;
            color: var(--primary);
            margin-bottom: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .back-button {
            padding: 0.5rem 1rem;
            background: var(--input-bg);
            color: var(--primary);
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            transition: all 0.3s;
        }

        .back-button:hover {
            background: var(--primary);
            color: var(--white);
        }

        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }

        .alert-error {
            background-color: rgba(244, 67, 54, 0.1);
            color: var(--danger);
            border: 1px solid var(--danger);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-main);
        }

        .form-control {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 1px solid var(--input-border);
            border-radius: 8px;
            background-color: var(--input-bg);
            font-size: 1rem;
            transition: all 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(91, 109, 250, 0.1);
        }

        .form-control:disabled {
            background-color: #e9ecef;
            cursor: not-allowed;
        }

        .rental-summary {
            background-color: var(--input-bg);
            padding: 1.5rem;
            border-radius: 8px;
            margin-top: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .rental-summary h3 {
            color: var(--primary);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .rental-summary p {
            margin-bottom: 0.5rem;
            display: flex;
            justify-content: space-between;
        }

        .rental-summary .total {
            font-weight: 600;
            color: var(--primary);
            border-top: 1px solid var(--input-border);
            padding-top: 0.5rem;
            margin-top: 0.5rem;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s;
            display: inline-block;
        }

        .btn-primary {
            background-color: var(--primary);
            color: var(--white);
        }

        .btn-primary:hover {
            background-color: #4a5bd9;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background-color: var(--input-bg);
            color: var(--text-main);
        }

        .btn-secondary:hover {
            background-color: #e0e7ff;
            transform: translateY(-2px);
        }

        .action-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <jsp:include page="header.jsp" />

    <div class="main-content">
        <div class="page-title">
            <h1>Edit Rental</h1>
            <a href="${pageContext.request.contextPath}/user-rentals" class="back-button">
                <i class="fas fa-arrow-left"></i> Back to Rentals
            </a>
        </div>

        <div id="errorMessage" class="alert alert-error" style="display: none;">
            <i class="fas fa-exclamation-circle"></i> <span id="errorText"></span>
        </div>

        <form id="editRentalForm" action="${pageContext.request.contextPath}/edit-rental" method="post">
            <input type="hidden" name="rental_detail_id" value="<%= rental.getRentalDetailId() %>">

            <div class="form-group">
                <label for="book_id">Book ID</label>
                <input type="text" id="book_id" class="form-control" value="<%= rental.getBookId() %>" disabled>
            </div>

            <div class="form-group">
                <label for="rental_date">Rental Start Date</label>
                <input type="date" id="rental_date" name="rental_date" class="form-control"
                       value="<%= rental.getRentalDate().format(formatter) %>" min="<%= java.time.LocalDate.now().format(formatter) %>" required>
            </div>

            <div class="form-group">
                <label for="rental_period">Rental Period (days)</label>
                <select id="rental_period" name="rental_period" class="form-control" required>
                    <option value="7" <%= (rental.getReturnDate().toEpochDay() - rental.getRentalDate().toEpochDay() == 7) ? "selected" : "" %>>7 days</option>
                    <option value="14" <%= (rental.getReturnDate().toEpochDay() - rental.getRentalDate().toEpochDay() == 14) ? "selected" : "" %>>14 days</option>
                    <option value="30" <%= (rental.getReturnDate().toEpochDay() - rental.getRentalDate().toEpochDay() == 30) ? "selected" : "" %>>30 days</option>
                </select>
            </div>

            <div class="rental-summary">
                <h3>Rental Summary</h3>
                <p>
                    <span>Daily Rate:</span>
                    <span>Rs. 50</span>
                </p>
                <p>
                    <span>Selected Period:</span>
                    <span id="selected_period"><%= rental.getReturnDate().toEpochDay() - rental.getRentalDate().toEpochDay() %> days</span>
                </p>
                <p class="total">
                    <span>Total Amount:</span>
                    <span>Rs. <span id="total_amount"><%= rental.getRentalAmount() %></span></span>
                </p>
            </div>

            <div class="action-buttons">
                <a href="${pageContext.request.contextPath}/user-rentals" class="btn btn-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Changes
                </button>
            </div>
        </form>
    </div>

    <jsp:include page="footer.jsp" />

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const rentalPeriodSelect = document.getElementById('rental_period');
            const selectedPeriodSpan = document.getElementById('selected_period');
            const totalAmountSpan = document.getElementById('total_amount');
            const dailyRate = 50;

            // Update rental summary when period changes
            rentalPeriodSelect.addEventListener('change', function() {
                const period = parseInt(this.value);
                const totalAmount = period * dailyRate;

                selectedPeriodSpan.textContent = period + ' days';
                totalAmountSpan.textContent = totalAmount;
            });

            // Form validation
            const editRentalForm = document.getElementById('editRentalForm');
            const errorMessage = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');

            editRentalForm.addEventListener('submit', function(e) {
                const rentalDate = new Date(document.getElementById('rental_date').value);
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                if (rentalDate < today) {
                    e.preventDefault();
                    errorText.textContent = 'Rental date cannot be in the past.';
                    errorMessage.style.display = 'block';
                    return false;
                }

                return true;
            });
        });
    </script>

    <jsp:include page="footer.jsp" />
</body>
</html>
