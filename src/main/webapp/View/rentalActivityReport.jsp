<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%@ page import="java.util.Map" %>
<%@ page import="java.time.LocalDate" %>
<%@ page import="java.time.format.DateTimeFormatter" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
        response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
        return;
    }

    Map<String, Object> report = (Map<String, Object>) request.getAttribute("report");
    LocalDate startDate = (LocalDate) request.getAttribute("startDate");
    LocalDate endDate = (LocalDate) request.getAttribute("endDate");

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy");
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rental Activity Report - GyanKunja Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/responsive.css">
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --text-main: #222;
            --text-light: #666;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .main-content {
            padding: 20px 0;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
            flex-wrap: wrap;
            gap: 10px;
        }

        .page-title {
            font-size: 1.8rem;
            color: #333;
            margin: 0;
        }

        .page-subtitle {
            color: var(--text-light);
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 8px 16px;
            background-color: var(--primary);
            color: white;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .btn:hover {
            background-color: #4a5ce0;
        }

        .report-section {
            background: var(--white);
            border-radius: var(--radius);
            padding: 20px;
            box-shadow: var(--shadow);
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }

        .stat-card h3 {
            color: var(--text-light);
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .stat-card p {
            color: var(--primary);
            font-size: 1.8rem;
            font-weight: 600;
            margin: 0;
        }

        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 15px;
            }

            .stat-card {
                padding: 15px;
            }

            .stat-card p {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 576px) {
            .report-section {
                padding: 15px;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: 1fr 1fr;
            }

            .stat-card:last-child {
                grid-column: span 2;
            }
        }
    </style>
</head>
<body>
    <!-- Include header -->
    <jsp:include page="adminHeader.jsp" />

    <div class="container">
        <div class="main-content">
            <div class="report-section">
                <div class="page-header">
                    <div>
                        <h1 class="page-title">Rental Activity Report</h1>
                        <p class="page-subtitle">Data from <%= startDate.format(formatter) %> to <%= endDate.format(formatter) %></p>
                    </div>
                    <a href="${pageContext.request.contextPath}/admin/reports" class="btn">
                        <i class="fas fa-arrow-left"></i> Back to Reports
                    </a>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Total Rentals</h3>
                        <p><%= report.get("totalRentals") %></p>
                    </div>
                    <div class="stat-card">
                        <h3>Active Rentals</h3>
                        <p><%= report.get("activeRentals") %></p>
                    </div>
                    <div class="stat-card">
                        <h3>Completed Rentals</h3>
                        <p><%= report.get("completedRentals") %></p>
                    </div>
                    <div class="stat-card">
                        <h3>Pending Rentals</h3>
                        <p><%= report.get("pendingRentals") %></p>
                    </div>
                    <div class="stat-card">
                        <h3>Total Revenue</h3>
                        <p>₹<%= String.format("%.2f", report.get("totalRevenue")) %></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include footer -->
    <jsp:include page="footer.jsp" />
</body>
</html>
