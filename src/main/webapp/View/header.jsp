<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%
    User user = (User) session.getAttribute("user");
    String currentPage = request.getRequestURI();
%>
<header class="navbar">
    <div class="nav-top">
        <div class="logo-section">
            <a href="${pageContext.request.contextPath}/index.jsp" style="text-decoration: none; display: flex; align-items: center; gap: 1rem;">
                <img src="${pageContext.request.contextPath}/assets/images/books/Gyankunj.png" alt="GyanKunja Logo" style="height: 40px; width: auto;">
                <h1 class="website-name">GyanKunja</h1>
            </a>
        </div>
        <div class="search-section">
            <form action="${pageContext.request.contextPath}/search" method="get" class="search-container">
                <input type="text" name="query" class="search-input" placeholder="Search for books..." required>
                <button type="submit" class="search-button">Search</button>
            </form>
            <div class="auth-buttons">
                <% if (user == null) { %>
                    <a href="${pageContext.request.contextPath}/View/LogIn.jsp" class="nav-link login-btn">Login</a>
                    <a href="${pageContext.request.contextPath}/View/Register.jsp" class="nav-link register-btn">Register</a>
                <% } else { %>
                    <div class="user-info">
                        <span class="username"><%= user.getUserName() %></span>
                        <a href="${pageContext.request.contextPath}/ForLogOut" class="nav-link logout-btn">
                            <i class="fas fa-sign-out-alt"></i>
                        </a>
                    </div>
                <% } %>
            </div>
        </div>
        <button class="mobile-menu-toggle" id="mobileMenuToggle">
            <i class="fas fa-bars"></i>
        </button>
    </div>
    <div class="nav-bottom" id="navBottom">
        <div class="nav-links">
            <a href="${pageContext.request.contextPath}/index.jsp" class="nav-link <%= currentPage.endsWith("/index.jsp") ? "active" : "" %>">Home</a>
            <a href="${pageContext.request.contextPath}/View/category.jsp" class="nav-link <%= currentPage.endsWith("/category.jsp") || currentPage.endsWith("/simpleRentBook.jsp") || currentPage.endsWith("/rentBook.jsp") || currentPage.endsWith("/rentalSuccess.jsp") ? "active" : "" %>">Categories</a>
            <a href="${pageContext.request.contextPath}/View/UserDashboard.jsp" class="nav-link <%= currentPage.endsWith("/UserDashboard.jsp") || currentPage.endsWith("/viewRentals.jsp") || currentPage.endsWith("/editRental.jsp") ? "active" : "" %>">User</a>
            <a href="${pageContext.request.contextPath}/View/Contact.jsp" class="nav-link <%= currentPage.endsWith("/Contact.jsp") ? "active" : "" %>">Contact Us</a>
            <a href="${pageContext.request.contextPath}/View/about.jsp" class="nav-link <%= currentPage.endsWith("/about.jsp") ? "active" : "" %>">About Us</a>
        </div>
    </div>
</header>

<style>
    /* Navbar Styles */
    .navbar {
        background: var(--white);
        width: 100%;
        box-shadow: var(--shadow);
        position: relative;
        z-index: 1000;
    }

    .nav-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 2rem;
        border-bottom: 1px solid var(--input-border);
    }

    .logo-section {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .website-name {
        color: var(--primary);
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
    }

    .search-section {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex: 1;
        justify-content: flex-end;
    }

    .search-container {
        display: flex;
        align-items: center;
        background: var(--input-bg);
        border-radius: 50px;
        padding: 0.5rem 1rem;
        width: 300px;
    }

    .search-input {
        flex: 1;
        border: none;
        background: transparent;
        outline: none;
        font-size: 0.9rem;
        color: var(--text-main);
    }

    .search-button {
        background: none;
        border: none;
        color: var(--primary);
        cursor: pointer;
    }

    .auth-buttons {
        display: flex;
        gap: 1rem;
    }

    .nav-link {
        text-decoration: none;
        color: var(--text-main);
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .login-btn, .register-btn {
        padding: 0.5rem 1rem;
        border-radius: 50px;
    }

    .login-btn {
        background: var(--input-bg);
        color: var(--primary);
    }

    .register-btn {
        background: var(--primary);
        color: var(--white);
    }

    .nav-bottom {
        padding: 0.5rem 2rem;
        display: flex;
        justify-content: center;
    }

    .nav-links {
        display: flex;
        gap: 2rem;
    }

    .nav-link:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow);
    }

    .nav-link.active {
        color: var(--primary);
        font-weight: 600;
    }

    .user-info {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .username {
        color: var(--text-main);
        font-weight: 500;
    }

    .logout-btn {
        background: var(--accent);
        color: var(--white);
        padding: 0.5rem;
        border-radius: 50%;
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .logout-btn:hover {
        background: var(--primary);
    }

    .mobile-menu-toggle {
        display: none;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const navBottom = document.getElementById('navBottom');

        if (mobileMenuToggle && navBottom) {
            mobileMenuToggle.addEventListener('click', function() {
                navBottom.classList.toggle('active');
            });
        }
    });
</script>