<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%
    User user = (User) session.getAttribute("user");
    String currentPage = request.getRequestURI();
%>
<header class="navbar">
    <div class="nav-top">
        <div class="logo-section">
            <a href="${pageContext.request.contextPath}/index.jsp" style="text-decoration: none; display: flex; align-items: center; gap: 1rem;">
                <img src="${pageContext.request.contextPath}/assets/images/books/Gyankunj.png" alt="GyanKunja Logo" style="height: 40px; width: auto;">
                <h1 class="website-name">GyanKunja</h1>
            </a>
        </div>
        <div class="search-section">
            <div class="search-container">
                <input type="text" class="search-input" placeholder="Search...">
                <button class="search-button">Search</button>
            </div>
            <div class="auth-buttons">
                <% if (user == null) { %>
                    <a href="${pageContext.request.contextPath}/View/LogIn.jsp" class="nav-link login-btn">Login</a>
                    <a href="${pageContext.request.contextPath}/View/Register.jsp" class="nav-link register-btn">Register</a>
                <% } else { %>
                    <div class="user-info">
                        <span class="username"><%= user.getUserName() %></span>
                        <a href="${pageContext.request.contextPath}/ForLogOut" class="nav-link logout-btn">
                            <i class="fas fa-sign-out-alt"></i>
                        </a>
                    </div>
                <% } %>
            </div>
        </div>
        <button class="mobile-menu-toggle" id="mobileMenuToggle">
            <i class="fas fa-bars"></i>
        </button>
    </div>
    <div class="nav-bottom" id="navBottom">
        <div class="nav-links">
            <a href="${pageContext.request.contextPath}/index.jsp" class="nav-link <%= currentPage.endsWith("/index.jsp") ? "active" : "" %>">Home</a>
            <a href="${pageContext.request.contextPath}/View/category.jsp" class="nav-link <%= currentPage.endsWith("/category.jsp") || currentPage.endsWith("/simpleRentBook.jsp") || currentPage.endsWith("/rentBook.jsp") || currentPage.endsWith("/rentalSuccess.jsp") ? "active" : "" %>">Categories</a>
            <a href="${pageContext.request.contextPath}/View/UserDashboard.jsp" class="nav-link <%= currentPage.endsWith("/UserDashboard.jsp") || currentPage.endsWith("/viewRentals.jsp") || currentPage.endsWith("/editRental.jsp") || currentPage.endsWith("/viewRental.jsp") ? "active" : "" %>">User</a>
            <a href="${pageContext.request.contextPath}/View/Contact.jsp" class="nav-link <%= currentPage.endsWith("/Contact.jsp") ? "active" : "" %>">Contact Us</a>
            <a href="${pageContext.request.contextPath}/View/about.jsp" class="nav-link <%= currentPage.endsWith("/about.jsp") ? "active" : "" %>">About Us</a>
        </div>
    </div>
</header>

<style>
    .website-name {
        color: var(--primary);
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
    }

    .user-info {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .username {
        color: var(--text-main);
        font-weight: 500;
    }

    .logout-btn {
        background: var(--accent);
        color: var(--white);
        padding: 0.5rem;
        border-radius: 50%;
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .logout-btn:hover {
        background: var(--primary);
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const navBottom = document.getElementById('navBottom');

        if (mobileMenuToggle && navBottom) {
            mobileMenuToggle.addEventListener('click', function() {
                navBottom.classList.toggle('active');
            });
        }
    });
</script>