<%@ page isErrorPage="true" %>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%@ page import="Model.Book" %>
<%@ page import="java.time.LocalDate" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%
    // Get user from session
    User user = (User) session.getAttribute("user");
    if (user == null) {
        response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
        return;
    }

    Book book = (Book) request.getAttribute("book");
    String errorMessage = (String) request.getAttribute("errorMessage");
    String successMessage = (String) request.getAttribute("successMessage");

    // Debug information
    System.out.println("=== rentBook.jsp ===");
    System.out.println("User: " + (user != null ? user.getUserName() : "null"));
    System.out.println("Book: " + (book != null ? book.getTitle() : "null"));
    System.out.println("Error Message: " + errorMessage);
    System.out.println("Success Message: " + successMessage);
    System.out.println("book_id = " + request.getParameter("book_id"));

    // Set default image if imageUrl is null or empty
    String defaultImage = "assets/images/books/default-book.jpg";
    String bookImage = (book != null && book.getImageUrl() != null && !book.getImageUrl().isEmpty())
        ? book.getImageUrl()
        : defaultImage;
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rent Book | GyanKunja</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --accent: #b388ff;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --input-bg: #f0f4ff;
            --input-border: #e0e7ff;
            --text-main: #222;
            --text-light: #666;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Navbar Styles */
        .navbar {
            background: var(--white);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow);
        }

        .nav-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem 5%;
            border-bottom: 1px solid var(--input-border);
            gap: 3rem;
        }

        .nav-bottom {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0.5rem 5%;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--text-main);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary);
            background: var(--input-bg);
        }

        .nav-links a.active {
            color: var(--primary);
            background: var(--input-bg);
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .search-section {
            display: flex;
            align-items: center;
            gap: 2rem;
            width: 100%;
            justify-content: flex-end;
            margin-left: 2.5rem;
        }

        .search-container {
            display: flex;
            gap: 0.5rem;
        }

        .search-input {
            padding: 0.5rem 1rem;
            border: 1px solid var(--input-border);
            border-radius: 8px;
            width: 300px;
        }

        .search-button {
            background: var(--primary);
            color: var(--white);
            border: none;
            padding: 0.5rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
        }

        .nav-link {
            color: var(--text-main);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .login-btn {
            background: var(--primary);
            color: var(--white);
        }

        .register-btn {
            background: var(--accent);
            color: var(--white);
        }

        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .main-content {
            padding-top: 150px;
            padding-bottom: 80px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .book-details {
            background: var(--white);
            border-radius: var(--radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 2rem;
        }

        .book-image {
            width: 100%;
            max-width: 300px;
            border-radius: var(--radius);
            overflow: hidden;
        }

        .book-image img {
            width: 100%;
            height: auto;
            object-fit: cover;
        }

        .book-info h1 {
            font-size: 2rem;
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .book-meta {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            color: var(--text-light);
        }

        .book-meta span {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .book-description {
            margin-bottom: 2rem;
            color: var(--text-light);
        }

        .availability-status {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .available {
            background: #e6f7e6;
            color: #2e7d32;
        }

        .unavailable {
            background: #ffebee;
            color: #c62828;
        }

        .rental-form {
            background: var(--white);
            border-radius: var(--radius);
            padding: 2rem;
            box-shadow: var(--shadow);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-main);
            font-weight: 500;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--input-border);
            border-radius: var(--radius);
            background: var(--input-bg);
        }

        .submit-button {
            background: var(--primary);
            color: var(--white);
            border: none;
            padding: 1rem 2rem;
            border-radius: var(--radius);
            cursor: pointer;
            font-weight: 500;
            width: 100%;
            transition: all 0.3s;
        }

        .submit-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .error-message {
            color: #dc3545;
            margin-top: 0.5rem;
            font-size: 0.9rem;
            padding: 1rem;
            background: #ffebee;
            border-radius: var(--radius);
            margin-bottom: 1rem;
        }

        .success-message {
            color: #2e7d32;
            margin-top: 0.5rem;
            font-size: 0.9rem;
            padding: 1rem;
            background: #e6f7e6;
            border-radius: var(--radius);
            margin-bottom: 1rem;
        }

        .rental-summary {
            background: var(--input-bg);
            padding: 1.5rem;
            border-radius: var(--radius);
            margin-top: 2rem;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            color: var(--text-light);
        }

        .total-amount {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary);
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--input-border);
        }

        .login-prompt {
            text-align: center;
            padding: 2rem;
            background: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
        }

        .login-prompt a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
        }

        .login-prompt a:hover {
            text-decoration: underline;
        }

        .rent-button {
            display: inline-block;
            background: var(--primary);
            color: var(--white);
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
            cursor: pointer;
        }

        .rent-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            overflow-y: auto;
        }

        .modal-content {
            background: var(--white);
            margin: 5% auto;
            padding: 2rem;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            width: 90%;
            max-width: 800px;
            position: relative;
        }

        .close-modal {
            position: absolute;
            top: 1rem;
            right: 1rem;
            font-size: 1.5rem;
            color: var(--text-light);
            cursor: pointer;
            transition: color 0.3s;
        }

        .close-modal:hover {
            color: var(--primary);
        }

        .book-details {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .book-image {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .rent-button {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .rent-button:hover {
            background-color: #218838;
        }
        .modal-content {
            border-radius: 10px;
        }
        .modal-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .modal-body {
            padding: 20px;
        }
        .rental-summary {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .book-status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-weight: bold;
        }
        .status-available {
            background-color: #d4edda;
            color: #155724;
        }
        .status-unavailable {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <jsp:include page="header.jsp" />

    <div class="main-content">
        <div class="container">
            <% if (errorMessage != null) { %>
                <div class="error-message">
                    <%= errorMessage %>
                </div>
            <% } %>

            <% if (successMessage != null) { %>
                <div class="success-message">
                    <%= successMessage %>
                </div>
            <% } %>

            <% if (book != null) { %>
                <div class="book-details">
                    <div class="row">
                        <div class="col-md-4">
                            <img src="<%= bookImage %>" alt="<%= book.getTitle() %>" class="book-image"
                                 onerror="this.onerror=null; this.src='<%= defaultImage %>';">
                        </div>
                        <div class="col-md-8">
                            <h2><%= book.getTitle() %></h2>
                            <p class="text-muted">by <%= book.getAuthor() %></p>
                            <p><strong>Genre:</strong> <%= book.getGenre() %></p>
                            <p><strong>ISBN:</strong> <%= book.getIsbn() %></p>
                            <p><strong>Status:</strong>
                                <span class="book-status <%= book.isAvailable() ? "status-available" : "status-unavailable" %>">
                                    <%= book.isAvailable() ? "Available" : "Unavailable" %>
                                </span>
                            </p>
                            <p><strong>Description:</strong></p>
                            <p><%= book.getDescription() %></p>

                            <% if (book.isAvailable()) { %>
                                <button type="button" class="rent-button" data-bs-toggle="modal" data-bs-target="#rentModal">
                                    <i class="fas fa-book"></i> Rent Now
                                </button>
                            <% } %>
                        </div>
                    </div>
                </div>

                <!-- Rent Modal -->
                <div class="modal fade" id="rentModal" tabindex="-1" aria-labelledby="rentModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="rentModalLabel">Rent Book</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <img src="<%= bookImage %>" alt="<%= book.getTitle() %>" class="book-image mb-3">
                                        <h4><%= book.getTitle() %></h4>
                                        <p class="text-muted">by <%= book.getAuthor() %></p>
                                    </div>
                                    <div class="col-md-8">
                                        <% if (user == null) { %>
                                            <div class="login-prompt">
                                                <p>Please <a href="${pageContext.request.contextPath}/View/LogIn.jsp">login</a> to rent this book.</p>
                                            </div>
                                        <% } else { %>
                                            <form action="${pageContext.request.contextPath}/rent-book" method="post">
                                                <input type="hidden" name="book_id" value="<%= book.getBookId() %>">

                                                <div class="mb-3">
                                                    <label for="rental_date" class="form-label">Rental Start Date</label>
                                                    <input type="date" class="form-control" id="rental_date" name="rental_date"
                                                           value="<%= java.time.LocalDate.now() %>"
                                                           min="<%= java.time.LocalDate.now() %>" required>
                                                </div>

                                                <div class="mb-3">
                                                    <label for="rental_period" class="form-label">Rental Period (days)</label>
                                                    <select class="form-select" id="rental_period" name="rental_period" required>
                                                        <option value="7">7 days</option>
                                                        <option value="14">14 days</option>
                                                        <option value="30">30 days</option>
                                                    </select>
                                                </div>

                                                <div class="rental-summary">
                                                    <h5>Rental Summary</h5>
                                                    <p><strong>Daily Rate:</strong> Rs. 50</p>
                                                    <p><strong>Selected Period:</strong> <span id="selected_period">7</span> days</p>
                                                    <p><strong>Total Amount:</strong> Rs. <span id="total_amount">350</span></p>
                                                </div>

                                                <div class="d-grid gap-2 mt-3">
                                                    <button type="submit" class="btn btn-success">
                                                        <i class="fas fa-check"></i> Confirm Rental
                                                    </button>
                                                </div>
                                            </form>
                                        <% } %>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <% } else { %>
                <div class="alert alert-danger" role="alert">
                    Book not found. Please try again.
                </div>
            <% } %>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize the modal
            var rentModal = new bootstrap.Modal(document.getElementById('rentModal'));

            // Show modal when rent button is clicked
            $('.rent-button').on('click', function() {
                rentModal.show();
            });

            // Update rental summary when period changes
            $('#rental_period').on('change', function() {
                const period = $(this).val();
                const dailyRate = 50;
                const totalAmount = period * dailyRate;

                $('#selected_period').text(period);
                $('#total_amount').text(totalAmount);
            });

            // Set minimum date to today
            const today = new Date().toISOString().split('T')[0];
            $('#rental_date').attr('min', today);
            $('#rental_date').val(today);

            // Trigger change event to update summary initially
            $('#rental_period').trigger('change');

            // Form validation
            $('form').on('submit', function(e) {
                const rentalDate = $('#rental_date').val();
                if (!rentalDate) {
                    e.preventDefault();
                    alert('Please select a rental date');
                    return false;
                }
                return true;
            });
        });
    </script>

    <jsp:include page="footer.jsp" />
</body>
</html>