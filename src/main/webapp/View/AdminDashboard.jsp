<%--
  Created by IntelliJ IDEA.
  User: nitro
  Date: 4/21/2025
  Time: 10:31 AM
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%@ page import="Model.Book" %>
<%@ page import="Model.RentalDetails" %>
<%@ page import="java.util.List" %>
<%@ page import="java.time.format.DateTimeFormatter" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
        response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
        return;
    }

    // Get dashboard statistics
    Integer totalUsers = (Integer) request.getAttribute("totalUsers");
    Integer activeRentals = (Integer) request.getAttribute("activeRentals");
    Integer pendingRentals = (Integer) request.getAttribute("pendingRentals");
    Double totalRevenue = (Double) request.getAttribute("totalRevenue");

    // Get recent users
    List<User> recentUsers = (List<User>) request.getAttribute("recentUsers");

    // Get recent rentals
    List<RentalDetails> recentRentals = (List<RentalDetails>) request.getAttribute("recentRentals");

    // Get popular books
    List<Book> popularBooks = (List<Book>) request.getAttribute("popularBooks");

    // Format dates
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMM yyyy");

    // If the page is loaded directly without going through the servlet
    if (totalUsers == null) {
        response.sendRedirect(request.getContextPath() + "/admin/dashboard");
        return;
    }
%>
<html>
<head>
    <title>Admin Dashboard</title>
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --accent: #b388ff;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --input-bg: #f0f4ff;
            --input-border: #e0e7ff;
            --text-main: #222;
            --text-light: #666;
            --danger: #ff4444;
            --success: #00C851;
            --warning: #ffbb33;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Navbar Styles */
        .navbar {
            background: var(--white);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow);
        }

        .nav-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem 5%;
            border-bottom: 1px solid var(--input-border);
        }

        .nav-bottom {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0.5rem 5%;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--text-main);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary);
            background: var(--input-bg);
        }

        .nav-links a.active {
            color: var(--primary);
            background: var(--input-bg);
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .search-section {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .search-container {
            display: flex;
            gap: 0.5rem;
        }

        .search-input {
            padding: 0.5rem 1rem;
            border: 1px solid var(--input-border);
            border-radius: 8px;
            width: 300px;
        }

        .search-button {
            background: var(--primary);
            color: var(--white);
            border: none;
            padding: 0.5rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
        }

        .nav-link {
            color: var(--text-main);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .login-btn {
            background: var(--primary);
            color: var(--white);
        }

        .register-btn {
            background: var(--accent);
            color: var(--white);
        }

        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        /* Main Content Styles */
        .main-content {
            padding-top: 30px;
            padding-bottom: 80px;
        }

        .container {
            max-width: 1200px;
            margin: 10px auto 50px;
            padding: 20px;
            flex: 1;
        }

        /* Admin Panel Styles */
        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .admin-header h1 {
            font-size: 2rem;
            color: var(--primary);
        }

        .admin-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--white);
            border-radius: var(--radius);
            padding: 1.5rem;
            box-shadow: var(--shadow);
        }

        .stat-card h3 {
            color: var(--text-light);
            font-size: 1rem;
            margin-bottom: 0.5rem;
        }

        .stat-card p {
            font-size: 2rem;
            font-weight: 600;
            color: var(--primary);
        }

        .admin-tabs {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid var(--input-border);
            padding-bottom: 1rem;
        }

        .admin-tab {
            padding: 0.5rem 1rem;
            border-radius: var(--radius);
            cursor: pointer;
            transition: all 0.3s;
        }

        .admin-tab.active {
            background: var(--primary);
            color: var(--white);
        }

        .admin-tab:hover:not(.active) {
            background: var(--input-bg);
        }

        .admin-content {
            background: var(--white);
            border-radius: var(--radius);
            padding: 2rem;
            box-shadow: var(--shadow);
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--input-border);
        }

        th {
            background: var(--input-bg);
            font-weight: 600;
        }

        tr:hover {
            background: var(--input-bg);
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-view {
            background: var(--primary);
            color: var(--white);
        }

        .btn-edit {
            background: var(--warning);
            color: var(--white);
        }

        .btn-delete {
            background: var(--danger);
            color: var(--white);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .status-active {
            background: rgba(0, 200, 81, 0.1);
            color: var(--success);
        }

        .status-pending {
            background: rgba(255, 187, 51, 0.1);
            color: var(--warning);
        }

        .status-cancelled {
            background: rgba(255, 68, 68, 0.1);
            color: var(--danger);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            background: var(--white);
            border-radius: var(--radius);
            padding: 2rem;
            max-width: 500px;
            margin: 2rem auto;
            position: relative;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-header h2 {
            color: var(--primary);
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-light);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid var(--input-border);
            border-radius: 8px;
            background: var(--input-bg);
            transition: all 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            background: var(--white);
        }

        /* Footer Styles */
        .footer {
            background: var(--white);
            padding: 2rem 5%;
            border-top: 1px solid var(--input-border);
            margin-top: auto;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            gap: 4rem;
            padding-bottom: 2rem;
        }

        .footer-left {
            max-width: 400px;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .footer-logo img {
            height: 30px;
        }

        .footer-logo h2 {
            font-size: 1.25rem;
            color: var(--primary);
            font-weight: 600;
        }

        .footer-description {
            color: var(--text-light);
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .footer-right {
            display: flex;
            gap: 4rem;
        }

        .footer-section {
            min-width: 140px;
        }

        .footer-section h3 {
            color: var(--primary);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--input-border);
        }

        .footer-links {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .footer-links a {
            color: var(--text-light);
            text-decoration: none;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .footer-links a:hover {
            color: var(--primary);
            transform: translateX(5px);
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .social-links a {
            color: var(--text-light);
            text-decoration: none;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            padding: 0.5rem;
            border-radius: 50%;
            background: var(--input-bg);
        }

        .social-links a:hover {
            color: var(--primary);
            background: var(--primary);
            color: var(--white);
            transform: translateY(-3px);
        }

        .footer-bottom {
            max-width: 1200px;
            margin: 0 auto;
            padding-top: 1rem;
            border-top: 1px solid var(--input-border);
            text-align: center;
            color: var(--text-light);
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .admin-stats {
                grid-template-columns: 1fr;
            }

            .nav-top {
                flex-direction: column;
                gap: 1rem;
            }

            .search-section {
                flex-direction: column;
                width: 100%;
            }

            .search-container {
                width: 100%;
            }

            .search-input {
                width: 100%;
            }

            .footer-content {
                flex-direction: column;
                gap: 2rem;
            }

            .footer-right {
                flex-wrap: wrap;
                gap: 2rem;
            }

            .footer-section {
                min-width: 120px;
            }
        }
    </style>
</head>
<body>
    <jsp:include page="adminHeader.jsp" />

    <div class="container">
        <div class="admin-header">
            <h1 class="admin-title">Admin Dashboard</h1>
        </div>

        <div class="admin-stats">
            <div class="stat-card">
                <h3>Total Users</h3>
                <p><%= totalUsers %></p>
            </div>
            <div class="stat-card">
                <h3>Active Bookings</h3>
                <p><%= activeRentals %></p>
            </div>
            <div class="stat-card">
                <h3>Pending Requests</h3>
                <p><%= pendingRentals %></p>
            </div>
            <div class="stat-card">
                <h3>Total Revenue</h3>
                <p>₹<%= String.format("%.2f", totalRevenue) %></p>
            </div>
        </div>

        <div class="admin-tabs">
            <div class="admin-tab active" onclick="showTab('users')">Users</div>
            <div class="admin-tab" onclick="showTab('bookings')">Bookings</div>
            <div class="admin-tab" onclick="showTab('books')">Books</div>
            <div class="admin-tab" onclick="showTab('rentals')">Rental Requests</div>
            <div class="admin-tab" onclick="showTab('reports')">Reports</div>
            <div class="admin-tab" onclick="showTab('settings')">Settings</div>
        </div>

        <div class="admin-quick-links" style="margin: 20px 0; display: flex; gap: 10px;">
            <a href="${pageContext.request.contextPath}/admin/users" class="btn btn-primary">Manage Users</a>
            <a href="${pageContext.request.contextPath}/admin/books" class="btn btn-primary">Manage Books</a>
            <a href="${pageContext.request.contextPath}/admin/books/add" class="btn btn-success">Add New Book</a>
            <a href="${pageContext.request.contextPath}/admin/rentals" class="btn btn-primary">Manage Rentals</a>
            <a href="${pageContext.request.contextPath}/admin/database-diagnostic" class="btn btn-info" style="background-color: #17a2b8; color: white;">Database Diagnostic</a>
        </div>

        <div class="admin-content">
            <!-- Users Tab -->
            <div id="users-tab" class="tab-content">
                <div class="table-container">
                    <table>
                        <thead>
                        <tr>
                            <th>User ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Join Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                        </thead>
                        <tbody>
                        <% if (recentUsers != null && !recentUsers.isEmpty()) { %>
                            <% for (User u : recentUsers) { %>
                            <tr>
                                <td>#<%= u.getUserId() %></td>
                                <td><%= u.getFullName() != null ? u.getFullName() : u.getUserName() %></td>
                                <td><%= u.getEmail() %></td>
                                <td>N/A</td>
                                <td>N/A</td>
                                <td>
                                    <% if ("admin".equalsIgnoreCase(u.getUserType())) { %>
                                        <span class="status-badge status-admin">Admin</span>
                                    <% } else { %>
                                        <span class="status-badge status-active">User</span>
                                    <% } %>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="${pageContext.request.contextPath}/admin/users/view/<%= u.getUserId() %>" class="btn btn-view">View</a>
                                        <a href="${pageContext.request.contextPath}/admin/users/edit/<%= u.getUserId() %>" class="btn btn-edit">Edit</a>
                                        <button class="btn btn-delete" onclick="deleteUser(<%= u.getUserId() %>)">Delete</button>
                                    </div>
                                </td>
                            </tr>
                            <% } %>
                        <% } else { %>
                            <tr>
                                <td colspan="7" style="text-align: center;">No users found</td>
                            </tr>
                        <% } %>
                        </tbody>
                    </table>
                </div>
                <div class="view-all-link">
                    <a href="${pageContext.request.contextPath}/admin/users" class="btn btn-primary">View All Users</a>
                </div>
            </div>

            <!-- Bookings Tab -->
            <div id="bookings-tab" class="tab-content" style="display: none;">
                <div class="table-container">
                                    <table>
                                        <thead>
                                        <tr>
                                            <th>Booking ID</th>
                                            <th>User</th>
                                            <th>Book</th>
                                            <th>Rental Date</th>
                                            <th>Return Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td>#B001</td>
                                            <td>John Doe</td>
                                            <td>The Great Gatsby</td>
                                            <td>2024-02-01</td>
                                            <td>2024-02-15</td>
                                            <td><span class="status-badge status-active">Active</span></td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn btn-view" onclick="viewBookingDetails('B001')">View</button>
                                                    <button class="btn btn-edit" onclick="editBooking('B001')">Edit</button>
                                                    <button class="btn btn-delete" onclick="deleteBooking('B001')">Delete</button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>#B002</td>
                                            <td>Jane Smith</td>
                                            <td>To Kill a Mockingbird</td>
                                            <td>2024-02-05</td>
                                            <td>2024-02-19</td>
                                            <td><span class="status-badge status-pending">Pending</span></td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn btn-view" onclick="viewBookingDetails('B002')">View</button>
                                                    <button class="btn btn-edit" onclick="editBooking('B002')">Edit</button>
                                                    <button class="btn btn-delete" onclick="deleteBooking('B002')">Delete</button>
                                                </div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Books Tab -->
                            <div id="books-tab" class="tab-content" style="display: none;">
                                <div class="action-header">
                                    <h3>Manage Books</h3>
                                    <button class="btn btn-add" onclick="addNewBook()">Add New Book</button>
                                </div>
                                <div class="table-container">
                                    <table>
                                        <thead>
                                        <tr>
                                            <th>Book ID</th>
                                            <th>Title</th>
                                            <th>Author</th>
                                            <th>Genre</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <% if (popularBooks != null && !popularBooks.isEmpty()) { %>
                                            <% for (Book book : popularBooks) { %>
                                            <tr>
                                                <td>#<%= book.getBookId() %></td>
                                                <td><%= book.getTitle() %></td>
                                                <td><%= book.getAuthor() %></td>
                                                <td><%= book.getGenre() %></td>
                                                <td>
                                                    <% if (book.isAvailable()) { %>
                                                        <span class="status-badge status-available">Available</span>
                                                    <% } else { %>
                                                        <span class="status-badge status-unavailable">Unavailable</span>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <a href="${pageContext.request.contextPath}/admin/books/view/<%= book.getBookId() %>" class="btn btn-view">View</a>
                                                        <a href="${pageContext.request.contextPath}/admin/books/edit/<%= book.getBookId() %>" class="btn btn-edit">Edit</a>
                                                        <button class="btn btn-delete" onclick="deleteBook(<%= book.getBookId() %>)">Delete</button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <% } %>
                                        <% } else { %>
                                            <tr>
                                                <td colspan="6" style="text-align: center;">No books found</td>
                                            </tr>
                                        <% } %>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Rentals Tab -->
                            <div id="rentals-tab" class="tab-content" style="display: none;">
                                <div class="action-header">
                                    <h3>Manage Rental Requests</h3>
                                    <a href="${pageContext.request.contextPath}/admin/rentals" class="btn btn-add">View All Rental Requests</a>
                                </div>

                                <% if (recentRentals != null && !recentRentals.isEmpty()) { %>
                                <div class="table-container">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>User</th>
                                                <th>Book</th>
                                                <th>Rental Date</th>
                                                <th>Return Date</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <% for (RentalDetails rental : recentRentals) { %>
                                            <tr>
                                                <td>#<%= rental.getRentalDetailId() %></td>
                                                <td><%= rental.getUserName() != null ? rental.getUserName() : "User #" + rental.getUserId() %></td>
                                                <td><%= rental.getBookTitle() != null ? rental.getBookTitle() : "Book #" + rental.getBookId() %></td>
                                                <td><%= rental.getRentalDate().format(formatter) %></td>
                                                <td><%= rental.getReturnDate().format(formatter) %></td>
                                                <td>
                                                    <% if ("PENDING".equals(rental.getRentalStatus())) { %>
                                                        <span class="status-badge status-pending">Pending</span>
                                                    <% } else if ("ACTIVE".equals(rental.getRentalStatus())) { %>
                                                        <span class="status-badge status-active">Active</span>
                                                    <% } else if ("COMPLETED".equals(rental.getRentalStatus())) { %>
                                                        <span class="status-badge status-completed">Completed</span>
                                                    <% } else if ("CANCELLED".equals(rental.getRentalStatus())) { %>
                                                        <span class="status-badge status-cancelled">Cancelled</span>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <a href="${pageContext.request.contextPath}/admin/rentals/view/<%= rental.getRentalDetailId() %>" class="btn btn-view">View</a>
                                                        <% if ("PENDING".equals(rental.getRentalStatus())) { %>
                                                            <a href="${pageContext.request.contextPath}/admin/rental-approval?action=approve&rentalId=<%= rental.getRentalDetailId() %>" class="btn btn-approve">Approve</a>
                                                            <a href="${pageContext.request.contextPath}/admin/rental-approval?action=reject&rentalId=<%= rental.getRentalDetailId() %>" class="btn btn-reject">Reject</a>
                                                        <% } %>
                                                    </div>
                                                </td>
                                            </tr>
                                            <% } %>
                                        </tbody>
                                    </table>
                                </div>
                                <% } else { %>
                                <p>No recent rental requests found. Click the button above to view all rental requests.</p>
                                <% } %>
                            </div>

                            <!-- Reports Tab -->
                            <div id="reports-tab" class="tab-content" style="display: none;">
                                <div class="table-container">
                                    <table>
                                        <thead>
                                        <tr>
                                            <th>Report ID</th>
                                            <th>Type</th>
                                            <th>Period</th>
                                            <th>Generated On</th>
                                            <th>Actions</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td>#R001</td>
                                            <td>Monthly Revenue</td>
                                            <td>January 2024</td>
                                            <td>2024-02-01</td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn btn-view">View</button>
                                                    <button class="btn btn-edit">Download</button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>#R002</td>
                                            <td>User Activity</td>
                                            <td>Last 30 Days</td>
                                            <td>2024-02-15</td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn btn-view">View</button>
                                                    <button class="btn btn-edit">Download</button>
                                                </div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Settings Tab -->
                            <div id="settings-tab" class="tab-content" style="display: none;">
                                <div class="action-header">
                                    <h3>System Settings</h3>
                                </div>
                                <div class="settings-container">
                                    <div class="form-group">
                                        <label>Site Name</label>
                                        <input type="text" class="form-control" value="GyanKunja">
                                    </div>
                                    <div class="form-group">
                                        <label>Admin Email</label>
                                        <input type="email" class="form-control" value="<EMAIL>">
                                    </div>
                                    <div class="form-group">
                                        <label>Rental Rate (per day)</label>
                                        <input type="number" class="form-control" value="50">
                                    </div>
                                    <div class="form-group">
                                        <label>Maximum Rental Period (days)</label>
                                        <input type="number" class="form-control" value="30">
                                    </div>
                                    <div class="form-group">
                                        <label>Late Fee (per day)</label>
                                        <input type="number" class="form-control" value="10">
                                    </div>
                                    <button class="btn btn-view" style="margin-top: 1rem;">Save Settings</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Details Modal -->
                <div id="userModal" class="modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h2>User Details</h2>
                            <button class="close-modal" onclick="closeModal('userModal')">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label>User ID</label>
                                <input type="text" class="form-control" value="#1001" readonly>
                            </div>
                            <div class="form-group">
                                <label>Full Name</label>
                                <input type="text" class="form-control" value="John Doe">
                            </div>
                            <div class="form-group">
                                <label>Email</label>
                                <input type="email" class="form-control" value="<EMAIL>">
                            </div>
                            <div class="form-group">
                                <label>Phone</label>
                                <input type="tel" class="form-control" value="+91 9876543210">
                            </div>
                            <div class="form-group">
                                <label>Address</label>
                                <textarea class="form-control">123, Book Street, Knowledge City</textarea>
                            </div>
                            <div class="form-group">
                                <label>Status</label>
                                <select class="form-control">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="suspended">Suspended</option>
                                </select>
                            </div>
                            <button class="btn btn-view" style="width: 100%; margin-top: 1rem;">Save Changes</button>
                        </div>
                    </div>
                </div>

                <!-- Booking Details Modal -->
                <div id="bookingModal" class="modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h2>Booking Details</h2>
                            <button class="close-modal" onclick="closeModal('bookingModal')">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label>Booking ID</label>
                                <input type="text" class="form-control" value="#B001" readonly>
                            </div>
                            <div class="form-group">
                                <label>User</label>
                                <input type="text" class="form-control" value="John Doe" readonly>
                            </div>
                            <div class="form-group">
                                <label>Book</label>
                                <input type="text" class="form-control" value="The Great Gatsby">
                            </div>
                            <div class="form-group">
                                <label>Rental Date</label>
                                <input type="date" class="form-control" value="2024-02-01">
                            </div>
                            <div class="form-group">
                                <label>Return Date</label>
                                <input type="date" class="form-control" value="2024-02-15">
                            </div>
                            <div class="form-group">
                                <label>Status</label>
                                <select class="form-control">
                                    <option value="active">Active</option>
                                    <option value="pending">Pending</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                            <button class="btn btn-view" style="width: 100%; margin-top: 1rem;">Save Changes</button>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <footer class="footer">
                    <div class="footer-content">
                        <div class="footer-left">
                            <div class="footer-logo">
                                <img src="css/logo.png" alt="GyanKunja Logo">
                                <h2>GyanKunja</h2>
                            </div>
                            <p class="footer-description">
                                Your one-stop destination for knowledge and learning resources.
                                Discover, learn, and grow with our extensive collection of books and materials.
                            </p>
                            <div class="social-links">
                                <a href="#">Facebook</a>
                                <a href="#">Twitter</a>
                                <a href="#">Instagram</a>
                                <a href="#">LinkedIn</a>
                            </div>
                        </div>

                        <div class="footer-right">
                            <div class="footer-section">
                                <h3>Quick Links</h3>
                                <div class="footer-links">
                                    <a href="${pageContext.request.contextPath}/index.jsp">Home</a>
                                    <a href="${pageContext.request.contextPath}/View/category.jsp">Categories</a>
                                    <a href="${pageContext.request.contextPath}/View/UserDashboard.jsp">User Dashboard</a>
                                    <a href="${pageContext.request.contextPath}/View/about.jsp">About Us</a>
                                    <a href="${pageContext.request.contextPath}/View/Contact.jsp">Contact Us</a>
                                </div>
                            </div>

                            <div class="footer-section">
                                <h3>Categories</h3>
                                <div class="footer-links">
                                    <a href="category.jsp#new">New Books</a>
                                    <a href="category.jsp#old">Old Books</a>
                                    <a href="category.jsp#recent">Recent Additions</a>
                                    <a href="category.jsp#popular">Popular Books</a>
                                </div>
                            </div>

                            <div class="footer-section">
                                <h3>Help & Support</h3>
                                <div class="footer-links">
                                    <a href="#">FAQ</a>
                                    <a href="#">Privacy Policy</a>
                                    <a href="#">Terms of Service</a>
                                    <a href="#">Support Center</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="footer-bottom">
                        <p>&copy; 2024 GyanKunja. All rights reserved.</p>
                    </div>
                </footer>

    <script>
    function showTab(tabName) {
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.style.display = 'none';
        });

        // Show selected tab content
        document.getElementById(tabName + '-tab').style.display = 'block';

        // Update active tab styling
        document.querySelectorAll('.admin-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        event.target.classList.add('active');
    }

    function viewUserDetails(userId) {
        document.getElementById('userModal').style.display = 'block';
    }

    function editUser(userId) {
        document.getElementById('userModal').style.display = 'block';
    }

    function deleteUser(userId) {
        if(confirm('Are you sure you want to delete this user?')) {
            // Add delete logic here
            alert('User deleted successfully!');
        }
    }

    function viewBookingDetails(bookingId) {
        document.getElementById('bookingModal').style.display = 'block';
    }

    function editBooking(bookingId) {
        document.getElementById('bookingModal').style.display = 'block';
    }

    function deleteBooking(bookingId) {
        if(confirm('Are you sure you want to delete this booking?')) {
            // Add delete logic here
            alert('Booking deleted successfully!');
        }
    }

    function addNewBook() {
        // Implement add new book functionality
        alert('Add new book functionality will be implemented here.');
    }

    function viewBookDetails(bookId) {
        // Implement view book details functionality
        alert('View book details for book ID: ' + bookId);
    }

    function editBook(bookId) {
        // Implement edit book functionality
        alert('Edit book with ID: ' + bookId);
    }

    function deleteBook(bookId) {
        if(confirm('Are you sure you want to delete this book?')) {
            // Add delete logic here
            alert('Book deleted successfully!');
        }
    }

    function closeModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
    }

    // Close modal when clicking outside
    window.onclick = function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.style.display = 'none';
        }
    }
    </script>
</body>
</html>
