<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%@ page import="Model.Book" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null || !"admin".equalsIgnoreCase(user.getUserType())) {
        response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
        return;
    }

    Book book = (Book) request.getAttribute("book");
    if (book == null) {
        response.sendRedirect(request.getContextPath() + "/admin/books");
        return;
    }
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Edit Book</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --accent: #b388ff;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --input-bg: #f0f4ff;
            --input-border: #e0e7ff;
            --text-main: #222;
            --text-light: #666;
            --success: #4CAF50;
            --warning: #FF9800;
            --danger: #F44336;
            --info: #2196F3;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            max-width: 800px;
            margin: 120px auto 50px;
            padding: 20px;
            flex: 1;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .admin-title {
            font-size: 24px;
            color: var(--primary);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background-color: var(--primary);
            color: var(--white);
        }

        .btn-primary:hover {
            background-color: #4a5bd9;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background-color: var(--input-bg);
            color: var(--text-main);
        }

        .btn-secondary:hover {
            background-color: #e0e7ff;
            transform: translateY(-2px);
        }

        .form-container {
            background-color: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-main);
        }

        .form-control {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid var(--input-border);
            border-radius: 8px;
            background-color: var(--input-bg);
            font-size: 14px;
            transition: all 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(91, 109, 250, 0.1);
        }

        textarea.form-control {
            min-height: 120px;
            resize: vertical;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 30px;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-success {
            background-color: rgba(76, 175, 80, 0.1);
            color: var(--success);
            border: 1px solid var(--success);
        }

        .alert-error {
            background-color: rgba(244, 67, 54, 0.1);
            color: var(--danger);
            border: 1px solid var(--danger);
        }

        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
        }

        .back-link:hover {
            text-decoration: underline;
        }

        .book-preview {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }

        .book-image {
            width: 120px;
            height: 180px;
            object-fit: cover;
            border-radius: 8px;
            box-shadow: var(--shadow);
        }

        .book-info {
            flex: 1;
        }

        .book-info h2 {
            color: var(--primary);
            margin-bottom: 10px;
        }

        .book-info p {
            color: var(--text-light);
            margin-bottom: 5px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
        }

        /* Image upload styles */
        .image-upload-container {
            margin-top: 10px;
        }

        .image-preview-container {
            margin: 15px 0;
            text-align: center;
        }

        .image-preview {
            max-width: 200px;
            max-height: 300px;
            border: 1px solid var(--input-border);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .upload-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .upload-status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-size: 14px;
        }

        .upload-status.uploading {
            background-color: #e0e7ff;
            color: var(--primary);
        }

        .upload-status.success {
            background-color: rgba(76, 175, 80, 0.1);
            color: #4CAF50;
        }

        .upload-status.error {
            background-color: rgba(244, 67, 54, 0.1);
            color: #F44336;
        }
    </style>
</head>
<body>
    <jsp:include page="adminHeader.jsp" />

    <div class="container">
        <a href="${pageContext.request.contextPath}/admin/books" class="back-link">
            <i class="fas fa-arrow-left"></i> Back to Books
        </a>

        <div class="admin-header">
            <h1 class="admin-title">Edit Book</h1>
        </div>

        <% if (request.getAttribute("successMessage") != null) { %>
            <div class="alert alert-success">
                <%= request.getAttribute("successMessage") %>
            </div>
        <% } %>

        <% if (request.getAttribute("errorMessage") != null) { %>
            <div class="alert alert-error">
                <%= request.getAttribute("errorMessage") %>
            </div>
        <% } %>

        <div class="book-preview">
            <img src="${pageContext.request.contextPath}/<%= book.getImageUrl() != null && !book.getImageUrl().isEmpty() ? book.getImageUrl() : "assets/images/books/default-book.jpg" %>"
                 alt="<%= book.getTitle() %>" class="book-image"
                 onerror="this.onerror=null; this.src='${pageContext.request.contextPath}/assets/images/books/default-book.jpg';">
            <div class="book-info">
                <h2><%= book.getTitle() %></h2>
                <p>by <%= book.getAuthor() %></p>
                <p>Genre: <%= book.getGenre() %></p>
                <p>ISBN: <%= book.getIsbn() != null ? book.getIsbn() : "N/A" %></p>
                <p>Status: <%= book.isAvailable() ? "Available" : "Unavailable" %></p>
            </div>
        </div>

        <div class="form-container">
            <form action="${pageContext.request.contextPath}/admin/books" method="post">
                <input type="hidden" name="action" value="update">
                <input type="hidden" name="bookId" value="<%= book.getBookId() %>">

                <div class="form-group">
                    <label for="title">Title *</label>
                    <input type="text" id="title" name="title" class="form-control" value="<%= book.getTitle() %>" required>
                </div>

                <div class="form-group">
                    <label for="author">Author *</label>
                    <input type="text" id="author" name="author" class="form-control" value="<%= book.getAuthor() %>" required>
                </div>

                <div class="form-group">
                    <label for="genre">Genre *</label>
                    <input type="text" id="genre" name="genre" class="form-control" value="<%= book.getGenre() %>" required>
                </div>

                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea id="description" name="description" class="form-control"><%= book.getDescription() != null ? book.getDescription() : "" %></textarea>
                </div>

                <div class="form-group">
                    <label for="isbn">ISBN</label>
                    <input type="text" id="isbn" name="isbn" class="form-control" value="<%= book.getIsbn() != null ? book.getIsbn() : "" %>">
                </div>

                <div class="form-group">
                    <label for="imageUrl">Book Cover Image</label>
                    <div class="image-upload-container">
                        <input type="text" id="imageUrl" name="imageUrl" class="form-control" value="<%= book.getImageUrl() != null ? book.getImageUrl() : "" %>" readonly>
                        <div class="image-preview-container">
                            <img id="imagePreview" src="${pageContext.request.contextPath}/<%= book.getImageUrl() != null && !book.getImageUrl().isEmpty() ? book.getImageUrl() : "assets/images/books/default-book.jpg" %>" alt="Book Cover Preview" class="image-preview"
                                 onerror="this.onerror=null; this.src='${pageContext.request.contextPath}/assets/images/books/default-book.jpg';">
                        </div>
                        <div class="upload-controls">
                            <input type="file" id="imageFile" accept="image/*" style="display: none;">
                            <button type="button" id="browseButton" class="btn btn-secondary">Browse...</button>
                            <button type="button" id="uploadButton" class="btn btn-primary" disabled>Upload Image</button>
                        </div>
                        <div id="uploadStatus" class="upload-status"></div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="available" name="available" <%= book.isAvailable() ? "checked" : "" %>>
                        <label for="available">Available for rent</label>
                    </div>
                </div>

                <div class="form-actions">
                    <a href="${pageContext.request.contextPath}/admin/books" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn btn-primary">Update Book</button>
                </div>
            </form>
        </div>
    </div>

    <jsp:include page="footer.jsp" />

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const imageFileInput = document.getElementById('imageFile');
            const browseButton = document.getElementById('browseButton');
            const uploadButton = document.getElementById('uploadButton');
            const imageUrlInput = document.getElementById('imageUrl');
            const imagePreview = document.getElementById('imagePreview');
            const uploadStatus = document.getElementById('uploadStatus');

            // Open file browser when browse button is clicked
            browseButton.addEventListener('click', function() {
                imageFileInput.click();
            });

            // Enable upload button when file is selected
            imageFileInput.addEventListener('change', function() {
                if (imageFileInput.files.length > 0) {
                    uploadButton.disabled = false;

                    // Show preview of selected image
                    const file = imageFileInput.files[0];
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        imagePreview.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                } else {
                    uploadButton.disabled = true;
                }
            });

            // Upload image when upload button is clicked
            uploadButton.addEventListener('click', function() {
                if (imageFileInput.files.length === 0) {
                    return;
                }

                const file = imageFileInput.files[0];
                const formData = new FormData();
                formData.append('image', file);

                uploadStatus.textContent = 'Uploading...';
                uploadStatus.className = 'upload-status uploading';

                fetch('${pageContext.request.contextPath}/upload-image', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        imageUrlInput.value = data.filePath;
                        uploadStatus.textContent = 'Upload successful!';
                        uploadStatus.className = 'upload-status success';
                    } else {
                        uploadStatus.textContent = 'Upload failed: ' + data.message;
                        uploadStatus.className = 'upload-status error';
                    }
                })
                .catch(error => {
                    uploadStatus.textContent = 'Upload failed: ' + error.message;
                    uploadStatus.className = 'upload-status error';
                });
            });
        });
    </script>
</body>
</html>
