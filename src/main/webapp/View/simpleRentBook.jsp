<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%@ page import="Model.Book" %>
<%@ page import="java.time.LocalDate" %>
<%@ page import="java.time.format.DateTimeFormatter" %>
<%
    User user = (User) session.getAttribute("user");
    if (user == null) {
        response.sendRedirect(request.getContextPath() + "/View/LogIn.jsp");
        return;
    }

    Book book = (Book) request.getAttribute("book");
    if (book == null) {
        response.sendRedirect(request.getContextPath() + "/index.jsp");
        return;
    }

    String errorMessage = (String) request.getAttribute("errorMessage");

    // Set default image if imageUrl is null or empty
    String defaultImage = "assets/images/books/default-book.jpg";
    String bookImage = (book.getImageUrl() != null && !book.getImageUrl().isEmpty())
        ? book.getImageUrl()
        : defaultImage;

    // Get current date and format it for the date input
    LocalDate today = LocalDate.now();
    DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    String todayFormatted = today.format(dateFormatter);

    // Calculate max date (6 months from now)
    LocalDate maxDate = today.plusMonths(6);
    String maxDateFormatted = maxDate.format(dateFormatter);
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rent Book - GyanKunja</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/responsive.css">
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --accent: #b388ff;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --input-bg: #f0f4ff;
            --input-border: #e0e7ff;
            --text-main: #222;
            --text-light: #666;
            --success: #4CAF50;
            --warning: #FF9800;
            --danger: #F44336;
            --info: #2196F3;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            max-width: 1000px;
            margin: 30px auto 50px;
            padding: 40px;
            background: var(--white);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            flex: 1;
            display: flex;
            gap: 2rem;
        }

        .book-preview {
            flex: 1;
            padding: 1.5rem;
            background-color: var(--input-bg);
            border-radius: var(--radius);
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .book-image {
            width: 200px;
            height: 300px;
            object-fit: cover;
            border-radius: 8px;
            box-shadow: var(--shadow);
            margin-bottom: 1.5rem;
        }

        .book-info h2 {
            color: var(--primary);
            margin-bottom: 0.5rem;
            font-size: 1.5rem;
            text-align: center;
        }

        .book-info p {
            color: var(--text-light);
            margin-bottom: 0.5rem;
            text-align: center;
        }

        .book-info .author {
            font-style: italic;
            margin-bottom: 1rem;
        }

        .book-info .description {
            margin-top: 1rem;
            text-align: justify;
        }

        .rental-form {
            flex: 1;
            padding: 1.5rem;
        }

        .rental-form h2 {
            color: var(--primary);
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-main);
        }

        .form-control {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 1px solid var(--input-border);
            border-radius: 8px;
            background-color: var(--input-bg);
            font-size: 1rem;
            transition: all 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(91, 109, 250, 0.1);
        }

        .rental-summary {
            background-color: var(--input-bg);
            padding: 1.5rem;
            border-radius: 8px;
            margin-top: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .rental-summary h3 {
            color: var(--primary);
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .rental-summary p {
            margin-bottom: 0.5rem;
            display: flex;
            justify-content: space-between;
        }

        .rental-summary .total {
            font-weight: 600;
            color: var(--primary);
            border-top: 1px solid var(--input-border);
            padding-top: 0.5rem;
            margin-top: 0.5rem;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s;
            display: inline-block;
        }

        .btn-primary {
            background-color: var(--primary);
            color: var(--white);
        }

        .btn-primary:hover {
            background-color: #4a5bd9;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background-color: var(--input-bg);
            color: var(--text-main);
        }

        .btn-secondary:hover {
            background-color: #e0e7ff;
            transform: translateY(-2px);
        }

        .action-buttons {
            display: flex;
            justify-content: space-between;
            gap: 1rem;
            margin-top: 2rem;
        }

        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }

        .alert-error {
            background-color: rgba(244, 67, 54, 0.1);
            color: var(--danger);
            border: 1px solid var(--danger);
        }

        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
                padding: 20px;
            }

            .book-image {
                width: 150px;
                height: 225px;
            }
        }
    </style>
</head>
<body>
    <jsp:include page="header.jsp" />

    <div class="main-content">
        <div class="book-preview">
            <img src="${pageContext.request.contextPath}/<%= bookImage %>" alt="<%= book.getTitle() %>" class="book-image"
                 onerror="this.onerror=null; this.src='${pageContext.request.contextPath}/<%= defaultImage %>';">
            <div class="book-info">
                <h2><%= book.getTitle() %></h2>
                <p class="author">by <%= book.getAuthor() %></p>
                <p>Genre: <%= book.getGenre() %></p>
                <p>ISBN: <%= book.getIsbn() != null ? book.getIsbn() : "N/A" %></p>
                <p class="description"><%= book.getDescription() != null ? book.getDescription() : "No description available." %></p>
            </div>
        </div>

        <div class="rental-form">
            <h2>Rent This Book</h2>

            <% if (errorMessage != null && !errorMessage.isEmpty()) { %>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i> <%= errorMessage %>
                </div>
            <% } %>

            <form id="rentBookForm" action="${pageContext.request.contextPath}/rent-book" method="post">
                <input type="hidden" name="book_id" value="<%= book.getBookId() %>">

                <div class="form-group">
                    <label for="rental_date">Rental Start Date</label>
                    <input type="date" id="rental_date" name="rental_date" class="form-control"
                           value="<%= todayFormatted %>" min="<%= todayFormatted %>" max="<%= maxDateFormatted %>" required>
                </div>

                <div class="form-group">
                    <label for="rental_period">Rental Period</label>
                    <select id="rental_period" name="rental_period" class="form-control" required>
                        <option value="7">7 days</option>
                        <option value="14">14 days</option>
                        <option value="30">30 days</option>
                    </select>
                </div>

                <div class="rental-summary">
                    <h3>Rental Summary</h3>
                    <p>
                        <span>Daily Rate:</span>
                        <span>Rs. 50</span>
                    </p>
                    <p>
                        <span>Selected Period:</span>
                        <span id="selected_period">7 days</span>
                    </p>
                    <p class="total">
                        <span>Total Amount:</span>
                        <span>Rs. <span id="total_amount">350</span></span>
                    </p>
                </div>

                <div class="action-buttons">
                    <a href="${pageContext.request.contextPath}/index.jsp" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-shopping-cart"></i> Rent Now
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const rentalPeriodSelect = document.getElementById('rental_period');
            const selectedPeriodSpan = document.getElementById('selected_period');
            const totalAmountSpan = document.getElementById('total_amount');
            const dailyRate = 50;

            // Update rental summary when period changes
            rentalPeriodSelect.addEventListener('change', function() {
                const period = parseInt(this.value);
                const totalAmount = period * dailyRate;

                selectedPeriodSpan.textContent = period + ' days';
                totalAmountSpan.textContent = totalAmount;
            });

            // Form validation
            const rentBookForm = document.getElementById('rentBookForm');

            rentBookForm.addEventListener('submit', function(e) {
                const rentalDate = new Date(document.getElementById('rental_date').value);
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                if (rentalDate < today) {
                    e.preventDefault();
                    alert('Rental date cannot be in the past.');
                    return false;
                }

                return true;
            });
        });
    </script>

    <jsp:include page="footer.jsp" />
</body>
</html>
