<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="Model.User" %>
<%
    User user = (User) session.getAttribute("user");
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>GyanKunja - Your Learning Platform</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/responsive.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        :root {
            --primary: #5b6dfa;
            --secondary: #34c9c9;
            --accent: #b388ff;
            --background: #f7faff;
            --white: #fff;
            --shadow: 0 8px 32px rgba(91, 109, 250, 0.10);
            --radius: 12px;
            --input-bg: #f0f4ff;
            --input-border: #e0e7ff;
            --text-main: #222;
            --text-light: #666;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        html, body {
            height: 100%;
        }

        body {
            background: var(--background);
            color: var(--text-main);
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        /* Navbar Styles */
        .navbar {
            background: var(--white);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow);
        }

        .nav-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem 5%;
            border-bottom: 1px solid var(--input-border);
            gap: 3rem;
        }

        .nav-bottom {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0.5rem 5%;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-links a {
            color: var(--text-main);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary);
            background: var(--input-bg);
        }

        .nav-links a.active {
            color: var(--primary);
            background: var(--input-bg);
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .search-section {
            display: flex;
            align-items: center;
            gap: 2rem;
            width: 100%;
            justify-content: flex-end;
            margin-left: 2.5rem;
        }

        .search-container {
            display: flex;
            gap: 0.5rem;
            margin-right: auto;
        }

        .search-input {
            padding: 0.5rem 1rem;
            border: 1px solid var(--input-border);
            border-radius: 8px;
            width: 300px;
        }

        .search-button {
            background: var(--primary);
            color: var(--white);
            border: none;
            padding: 0.5rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
            margin-left: auto;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-left: auto;
        }

        .username {
            color: var(--primary);
            font-weight: 500;
            font-size: 1rem;
        }

        .logout-btn {
            background: var(--primary);
            color: var(--white);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s;
        }

        .logout-btn:hover {
            background: var(--accent);
            transform: translateY(-2px);
        }

        .logout-btn i {
            font-size: 1.1rem;
        }

        .nav-link {
            color: var(--text-main);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .login-btn {
            background: var(--primary);
            color: var(--white);
        }

        .register-btn {
            background: var(--accent);
            color: var(--white);
        }

        .nav-link:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(120deg, var(--primary) 0%, var(--secondary) 100%);
            padding: 150px 5% 80px;
            color: var(--white);
            text-align: center;
        }

        .hero-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .hero-btn {
            padding: 0.8rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s;
        }

        .primary-btn {
            background: var(--white);
            color: var(--primary);
        }

        .secondary-btn {
            background: transparent;
            color: var(--white);
            border: 2px solid var(--white);
        }

        .hero-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        /* Categories Section */
        .categories {
            padding: 80px 5%;
            background: var(--white);
        }

        .section-title {
            text-align: center;
            margin-bottom: 3rem;
        }

        .section-title h2 {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .section-title p {
            color: var(--text-light);
            font-size: 1.1rem;
        }

        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .category-card {
            background: var(--white);
            border-radius: var(--radius);
            padding: 2rem;
            text-align: center;
            box-shadow: var(--shadow);
            transition: all 0.3s;
        }

        .category-card:hover {
            transform: translateY(-5px);
        }

        .category-icon {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 1rem;
        }

        .category-card h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: var(--text-main);
        }

        .category-card p {
            color: var(--text-light);
        }

        /* Popular Books Section */
        .popular-books {
            padding: 80px 5%;
            background: var(--background);
        }

        .book-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .book-card {
            background: var(--white);
            border-radius: var(--radius);
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: all 0.3s;
            display: flex;
            flex-direction: column;
        }

        .book-card:hover {
            transform: translateY(-5px);
        }

        .book-image-container {
            width: 100%;
            height: 300px;
            background: var(--input-bg);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px;
            overflow: hidden;
        }

        .book-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
        }

        .book-info {
            padding: 1.5rem;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .book-title {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            color: var(--text-main);
        }

        .book-author {
            color: var(--text-light);
            margin-bottom: 1rem;
        }

        .book-rating {
            color: #ffc107;
            margin-bottom: 1rem;
        }

        .book-price {
            font-weight: 600;
            color: var(--primary);
        }

        /* Testimonials Section */
        .testimonials {
            padding: 80px 5%;
            background: var(--white);
        }

        .testimonial-slider {
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            padding: 0 50px;
        }

        .testimonial-card {
            background: var(--white);
            border-radius: var(--radius);
            padding: 2rem;
            box-shadow: var(--shadow);
            height: auto;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .testimonial-text {
            font-style: italic;
            margin-bottom: 1.5rem;
            color: var(--text-main);
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .author-image {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
        }

        .author-info h4 {
            color: var(--text-main);
            margin-bottom: 0.2rem;
        }

        .author-info p {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        /* Swiper Slider Styles */
        .swiper {
            width: 100%;
            height: 100%;
        }

        .swiper-slide {
            text-align: left;
            display: flex;
            justify-content: center;
            align-items: center;
            height: auto;
        }

        .swiper-button-next,
        .swiper-button-prev {
            color: var(--primary);
        }

        .swiper-pagination-bullet-active {
            background: var(--primary);
        }

        /* Footer Styles */
        .footer {
            background: var(--white);
            padding: 2rem 5%;
            border-top: 1px solid var(--input-border);
            margin-top: auto;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            gap: 4rem;
            padding-bottom: 2rem;
        }

        .footer-left {
            max-width: 400px;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .footer-logo img {
            height: 30px;
        }

        .footer-logo h2 {
            font-size: 1.25rem;
            color: var(--primary);
            font-weight: 600;
        }

        .footer-description {
            color: var(--text-light);
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 1rem;
        }

        .footer-right {
            display: flex;
            gap: 4rem;
        }

        .footer-section {
            min-width: 140px;
        }

        .footer-section h3 {
            color: var(--primary);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--input-border);
        }

        .footer-links {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .footer-links a {
            color: var(--text-light);
            text-decoration: none;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .footer-links a:hover {
            color: var(--primary);
            transform: translateX(5px);
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .social-links a {
            color: var(--text-light);
            text-decoration: none;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            padding: 0.5rem;
            border-radius: 50%;
            background: var(--input-bg);
        }

        .social-links a:hover {
            color: var(--primary);
            background: var(--primary);
            color: var(--white);
            transform: translateY(-3px);
        }

        .footer-bottom {
            max-width: 1200px;
            margin: 0 auto;
            padding-top: 1rem;
            border-top: 1px solid var(--input-border);
            text-align: center;
            color: var(--text-light);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .footer-content {
                flex-direction: column;
                gap: 2rem;
            }

            .footer-right {
                flex-wrap: wrap;
                gap: 2rem;
            }

            .footer-section {
                min-width: 120px;
            }

            .category-grid,
            .book-grid,
            .testimonial-slider {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="navbar">
        <div class="nav-top">
            <div class="logo-section">
                <img src="assets/images/books/Gyankunj.png" alt="GyanKunja Logo" style="height: 40px; width: auto;">
                <h1 class="website-name">GyanKunja</h1>
            </div>
            <div class="search-section">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search...">
                    <button class="search-button">Search</button>
                </div>
                <div class="auth-buttons">
                    <% if (user == null) { %>
                        <a href="View/LogIn.jsp" class="nav-link login-btn">Login</a>
                        <a href="View/Register.jsp" class="nav-link register-btn">Register</a>
                    <% } else { %>
                        <div class="user-info">
                            <span class="username"><%= user.getUserName() %></span>
                            <a href="${pageContext.request.contextPath}/ForLogOut" class="nav-link logout-btn">
                                <i class="fas fa-sign-out-alt"></i>
                            </a>
                        </div>
                    <% } %>
                </div>
            </div>
            <button class="mobile-menu-toggle" id="mobileMenuToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        <div class="nav-bottom" id="navBottom">
            <div class="nav-links">
                <a href="index.jsp" class="nav-link active">Home</a>
                <a href="View/category.jsp" class="nav-link">Categories</a>
                <a href="View/UserDashboard.jsp" class="nav-link">User</a>
                <a href="View/Contact.jsp" class="nav-link">Contact Us</a>
                <a href="View/about.jsp" class="nav-link">About Us</a>
            </div>
        </div>
    </header>

    <main>
        <section class="hero">
            <div class="hero-content">
                <h1>Welcome to GyanKunja</h1>
                <p>Your one-stop platform for learning and knowledge sharing</p>
                <div class="hero-buttons">
                    <a href="View/LogIn.jsp" class="hero-btn primary-btn">Get Started</a>
                    <a href="View/category.jsp" class="hero-btn secondary-btn">Browse Books</a>
                </div>
            </div>
        </section>

        <section class="categories" id="categories">
            <div class="section-title">
                <h2>Explore Categories</h2>
                <p>Discover books across various genres and subjects</p>
            </div>
            <div class="category-grid">
                <div class="category-card">
                    <div class="category-icon">📚</div>
                    <h3>Romance</h3>
                    <p>Heartwarming love stories and romantic adventures</p>
                </div>
                <div class="category-card">
                    <div class="category-icon">😂</div>
                    <h3>Comedy</h3>
                    <p>Laugh-out-loud stories and humorous tales</p>
                </div>
                <div class="category-card">
                    <div class="category-icon">⚔️</div>
                    <h3>Action</h3>
                    <p>Thrilling adventures and action-packed stories</p>
                </div>
                <div class="category-card">
                    <div class="category-icon">👶</div>
                    <h3>Children</h3>
                    <p>Engaging stories for young readers</p>
                </div>
                <div class="category-card">
                    <div class="category-icon">🍳</div>
                    <h3>Cooking</h3>
                    <p>Delicious recipes and culinary adventures</p>
                </div>
                <div class="category-card">
                    <div class="category-icon">🔬</div>
                    <h3>Science</h3>
                    <p>Fascinating scientific discoveries and theories</p>
                </div>
            </div>
        </section>

        <section class="popular-books">
            <div class="section-title">
                <h2>Popular Books</h2>
                <p>Discover our most loved and trending books</p>
            </div>
            <div class="book-grid">
                <div class="book-card">
                    <div class="book-image-container">
                        <img src="assets/images/books/book-nonfiction-2.jpg" alt="Book Cover" class="book-image">
                    </div>
                    <div class="book-info">
                        <h3 class="book-title">The Great Adventure</h3>
                        <p class="book-author">By John Smith</p>
                        <div class="book-rating">★★★★★</div>
                        <p class="book-price">$19.99</p>
                    </div>
                </div>
                <div class="book-card">
                    <div class="book-image-container">
                        <img src="assets/images/books/book-nonfiction-4.jpg" alt="Book Cover" class="book-image">
                    </div>
                    <div class="book-info">
                        <h3 class="book-title">Love in the Time</h3>
                        <p class="book-author">By Jane Doe</p>
                        <div class="book-rating">★★★★☆</div>
                        <p class="book-price">$24.99</p>
                    </div>
                </div>
                <div class="book-card">
                    <div class="book-image-container">
                        <img src="assets/images/books/book-graphic-4.jpg" alt="Book Cover" class="book-image">
                    </div>
                    <div class="book-info">
                        <h3 class="book-title">Culinary Delights</h3>
                        <p class="book-author">By Chef Michael</p>
                        <div class="book-rating">★★★★★</div>
                        <p class="book-price">$29.99</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="testimonials">
            <div class="section-title">
                <h2>What Our Readers Say</h2>
                <p>Hear from our satisfied customers</p>
            </div>
            <div class="testimonial-slider">
                <!-- Slider main container -->
                <div class="swiper testimonialSwiper">
                    <!-- Additional required wrapper -->
                    <div class="swiper-wrapper">
                        <!-- Slides -->
                        <div class="swiper-slide">
                            <div class="testimonial-card">
                                <p class="testimonial-text">"GyanKunja has transformed my reading experience. The variety of books available is incredible, and the platform is so user-friendly!"</p>
                                <div class="testimonial-author">
                                    <img src="images/user1.jpg" alt="User" class="author-image">
                                    <div class="author-info">
                                        <h4>Sarah Johnson</h4>
                                        <p>Book Enthusiast</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card">
                                <p class="testimonial-text">"I love how easy it is to find books in my favorite categories. The recommendations are spot-on, and I've discovered so many new authors!"</p>
                                <div class="testimonial-author">
                                    <img src="images/user2.jpg" alt="User" class="author-image">
                                    <div class="author-info">
                                        <h4>Michael Chen</h4>
                                        <p>Avid Reader</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card">
                                <p class="testimonial-text">"The customer service is exceptional, and the book quality is always top-notch. I recommend GyanKunja to all my book-loving friends!"</p>
                                <div class="testimonial-author">
                                    <img src="images/user3.jpg" alt="User" class="author-image">
                                    <div class="author-info">
                                        <h4>Emily Davis</h4>
                                        <p>Literature Student</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card">
                                <p class="testimonial-text">"As a teacher, I find GyanKunja to be an invaluable resource for my students. The educational books are well-curated and perfect for classroom use."</p>
                                <div class="testimonial-author">
                                    <img src="images/user1.jpg" alt="User" class="author-image">
                                    <div class="author-info">
                                        <h4>Robert Wilson</h4>
                                        <p>High School Teacher</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card">
                                <p class="testimonial-text">"The delivery is always on time, and the books arrive in perfect condition. GyanKunja has become my go-to platform for all my reading needs!"</p>
                                <div class="testimonial-author">
                                    <img src="images/user2.jpg" alt="User" class="author-image">
                                    <div class="author-info">
                                        <h4>Priya Sharma</h4>
                                        <p>Regular Customer</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Navigation buttons -->
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                    <!-- Pagination -->
                    <div class="swiper-pagination"></div>
                </div>
            </div>
        </section>
    </main>

    <jsp:include page="View/footer.jsp" />
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Swiper for testimonials
            const testimonialSwiper = new Swiper('.testimonialSwiper', {
                slidesPerView: 1,
                spaceBetween: 30,
                loop: true,
                autoplay: {
                    delay: 3000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                breakpoints: {
                    640: {
                        slidesPerView: 1,
                        spaceBetween: 20,
                    },
                    768: {
                        slidesPerView: 2,
                        spaceBetween: 30,
                    },
                    1024: {
                        slidesPerView: 3,
                        spaceBetween: 30,
                    },
                },
            });

            // Mobile menu toggle
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const navBottom = document.getElementById('navBottom');

            if (mobileMenuToggle && navBottom) {
                mobileMenuToggle.addEventListener('click', function() {
                    navBottom.classList.toggle('active');
                });
            }
        });
    </script>
</body>
</html>